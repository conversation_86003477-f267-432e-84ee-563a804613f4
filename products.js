// Sample items data
let items = [
    {
        id: 1,
        itemCode: 'RAW001',
        barcode: '1234567890123',
        itemName: 'دقيق أبيض',
        itemType: 'raw_material',
        category: 'flour',
        description: 'دقيق أبيض عالي الجودة',
        costPrice: 25.00,
        sellingPrice: 30.00,
        currency: 'SAR',
        unit: 'kg',
        minStock: 50,
        maxStock: 500,
        shelfLife: 365,
        isActive: true,
        createdAt: '2024-01-01',
        // مخازن متعددة مع أرصدة منفصلة
        warehouseStocks: [
            {
                warehouseId: 1,
                warehouseName: 'المخزن الرئيسي',
                currentStock: 100,
                minStock: 30,
                maxStock: 300,
                openingQuantity: 80,
                openingValue: 2000,
                openingDate: '2024-01-01',
                openingNotes: 'رصيد افتتاحي للمخزن الرئيسي'
            },
            {
                warehouseId: 2,
                warehouseName: 'مخزن المواد الخام',
                currentStock: 50,
                minStock: 20,
                maxStock: 200,
                openingQuantity: 40,
                openingValue: 1000,
                openingDate: '2024-01-01',
                openingNotes: 'رصيد افتتاحي لمخزن المواد الخام'
            }
        ],
        totalCurrentStock: 150,
        totalOpeningQuantity: 120,
        totalOpeningValue: 3000
    },
    {
        id: 2,
        itemCode: 'FIN001',
        barcode: '1234567890124',
        itemName: 'خبز أبيض',
        itemType: 'finished_product',
        category: 'bread',
        description: 'خبز أبيض طازج',
        costPrice: 1.50,
        sellingPrice: 2.00,
        currency: 'SAR',
        unit: 'piece',
        minStock: 20,
        maxStock: 200,
        shelfLife: 2,
        isActive: true,
        createdAt: '2024-01-01',
        // مخازن متعددة مع أرصدة منفصلة
        warehouseStocks: [
            {
                warehouseId: 3,
                warehouseName: 'مخزن المنتجات النهائية',
                currentStock: 85,
                minStock: 20,
                maxStock: 200,
                openingQuantity: 50,
                openingValue: 75,
                openingDate: '2024-01-01',
                openingNotes: 'رصيد افتتاحي للمنتجات النهائية'
            }
        ],
        totalCurrentStock: 85,
        totalOpeningQuantity: 50,
        totalOpeningValue: 75
    },
    {
        id: 3,
        itemCode: 'RAW002',
        barcode: '1234567890125',
        itemName: 'سكر أبيض',
        itemType: 'raw_material',
        category: 'sugar',
        description: 'سكر أبيض ناعم',
        costPrice: 3.50,
        sellingPrice: 4.00,
        currency: 'SAR',
        unit: 'kg',
        minStock: 30,
        maxStock: 300,
        shelfLife: 730,
        isActive: true,
        createdAt: '2024-01-01',
        // مخازن متعددة مع أرصدة منفصلة
        warehouseStocks: [
            {
                warehouseId: 1,
                warehouseName: 'المخزن الرئيسي',
                currentStock: 15,
                minStock: 30,
                maxStock: 300,
                openingQuantity: 80,
                openingValue: 280,
                openingDate: '2024-01-01',
                openingNotes: 'رصيد افتتاحي للسكر'
            }
        ],
        totalCurrentStock: 15,
        totalOpeningQuantity: 80,
        totalOpeningValue: 280
    },
    {
        id: 4,
        itemCode: 'SRV001',
        barcode: '',
        itemName: 'تزيين كيك حسب الطلب',
        itemType: 'service',
        category: 'decoration',
        description: 'خدمة تزيين الكيك حسب طلب العميل مع إمكانية الكتابة والرسم',
        costPrice: 0,
        sellingPrice: 50.00,
        currency: 'SAR',
        unit: '',
        minStock: 0,
        maxStock: 0,
        shelfLife: 0,
        isActive: true,
        createdAt: '2024-01-01',
        // الخدمات لا تحتاج مخازن
        warehouseStocks: [],
        totalCurrentStock: 0,
        totalOpeningQuantity: 0,
        totalOpeningValue: 0,
        // Service-specific fields
        serviceDuration: 2,
        serviceDurationUnit: 'hours',
        servicePricingType: 'fixed',
        serviceLaborCost: 30.00,
        serviceMinOrder: 1,
        serviceRequiresBooking: true,
        serviceAvailableOnline: true,
        serviceRequirements: 'يجب تحديد التصميم المطلوب قبل 24 ساعة من التسليم',
        serviceRequiredMaterials: 'كيك جاهز، كريمة تزيين، ألوان طعام، أدوات تزيين'
    },
    {
        id: 5,
        itemCode: 'SRV002',
        barcode: '',
        itemName: 'توصيل للمنازل',
        itemType: 'service',
        category: 'delivery',
        description: 'خدمة توصيل الطلبات للمنازل داخل المدينة',
        costPrice: 5.00,
        sellingPrice: 15.00,
        currency: 'SAR',
        unit: '',
        minStock: 0,
        maxStock: 0,
        shelfLife: 0,
        isActive: true,
        createdAt: '2024-01-01',
        // الخدمات لا تحتاج مخازن
        warehouseStocks: [],
        totalCurrentStock: 0,
        totalOpeningQuantity: 0,
        totalOpeningValue: 0,
        // Service-specific fields
        serviceDuration: 1,
        serviceDurationUnit: 'hours',
        servicePricingType: 'fixed',
        serviceLaborCost: 10.00,
        serviceMinOrder: 20.00,
        serviceRequiresBooking: false,
        serviceAvailableOnline: true,
        serviceRequirements: 'الحد الأدنى للطلب 20 ريال، التوصيل خلال ساعة واحدة',
        serviceRequiredMaterials: 'أكياس التوصيل، مركبة التوصيل'
    }
];

// Check authentication
function checkAuth() {
    const session = localStorage.getItem('anwar_bakery_session') ||
                   sessionStorage.getItem('anwar_bakery_session');

    if (!session) {
        window.location.href = 'login.html';
        return null;
    }

    return JSON.parse(session);
}

// Load user info
function loadUserInfo() {
    const session = checkAuth();
    if (session) {
        document.getElementById('userFullName').textContent = session.fullName;
        document.getElementById('userRole').textContent = session.role;
    }
}

// Load company info
function loadCompanyInfo() {
    const savedData = localStorage.getItem('anwar_bakery_company');
    if (savedData) {
        const companyData = JSON.parse(savedData);

        if (companyData.companyNameAr) {
            document.getElementById('sidebarCompanyName').textContent = companyData.companyNameAr;
            document.title = `إدارة الأصناف والمنتجات - ${companyData.companyNameAr}`;
        }
    }
}

// Load items from localStorage
function loadItems() {
    const savedItems = localStorage.getItem('anwar_bakery_items');
    if (savedItems) {
        items = JSON.parse(savedItems);
    }
    renderItems();
    updateStats();
    populateUnits();
    populateWarehouses(); // Load warehouses instead of branches
    populateAllWarehouses(); // Populate all warehouse selects
}

// Populate units from units system
function populateUnits() {
    const savedUnits = localStorage.getItem('anwar_bakery_units');
    let units = [];

    if (savedUnits) {
        units = JSON.parse(savedUnits);
    }

    // Populate small unit dropdown
    const smallUnitSelect = document.getElementById('smallUnit');
    if (smallUnitSelect) {
        smallUnitSelect.innerHTML = '<option value="">اختر الوحدة الصغرى</option>';

        units.filter(unit => unit.isActive).forEach(unit => {
            const option = document.createElement('option');
            option.value = unit.id;
            option.textContent = unit.smallUnitName || unit.unitName;
            option.dataset.unitName = unit.smallUnitName || unit.unitName;
            option.dataset.largeUnitName = unit.largeUnitName;
            option.dataset.conversionFactor = unit.conversionFactor;
            option.dataset.unitData = JSON.stringify(unit);
            smallUnitSelect.appendChild(option);
        });
    }

    // Populate large unit dropdown
    const largeUnitSelect = document.getElementById('largeUnit');
    if (largeUnitSelect) {
        largeUnitSelect.innerHTML = '<option value="">اختر الوحدة الكبرى</option>';

        units.filter(unit => unit.isActive).forEach(unit => {
            const option = document.createElement('option');
            option.value = unit.id;
            option.textContent = unit.largeUnitName || unit.unitName;
            option.dataset.unitName = unit.largeUnitName || unit.unitName;
            option.dataset.smallUnitName = unit.smallUnitName;
            option.dataset.conversionFactor = unit.conversionFactor;
            option.dataset.unitData = JSON.stringify(unit);
            largeUnitSelect.appendChild(option);
        });
    }

    // Backward compatibility - populate old unit field if exists
    const unitSelect = document.getElementById('unit');
    if (unitSelect) {
        unitSelect.innerHTML = '<option value="">اختر وحدة القياس</option>';

        units.filter(unit => unit.isActive).forEach(unit => {
            const option = document.createElement('option');
            option.value = unit.id;
            option.textContent = unit.unitName;
            option.dataset.largeUnit = unit.largeUnitName;
            option.dataset.smallUnit = unit.smallUnitName;
            option.dataset.conversionFactor = unit.conversionFactor;
            option.dataset.unitData = JSON.stringify(unit);
            unitSelect.appendChild(option);
        });
    }

    // Add event listeners for automatic price calculation
    addUnitChangeListeners();
}

// Add event listeners for unit changes
function addUnitChangeListeners() {
    const smallUnitSelect = document.getElementById('smallUnit');
    const largeUnitSelect = document.getElementById('largeUnit');
    const costPriceInput = document.getElementById('costPrice');

    if (smallUnitSelect) {
        smallUnitSelect.addEventListener('change', function() {
            updateUnitInformation();
            calculatePricesFromCostPrice();
        });
    }

    if (largeUnitSelect) {
        largeUnitSelect.addEventListener('change', function() {
            updateUnitInformation();
            calculatePricesFromCostPrice();
        });
    }

    if (costPriceInput) {
        costPriceInput.addEventListener('input', function() {
            calculatePricesFromCostPrice();
        });
    }
}

// Update unit information based on selected units
function updateUnitInformation() {
    const smallUnitSelect = document.getElementById('smallUnit');
    const largeUnitSelect = document.getElementById('largeUnit');
    const conversionFactorInput = document.getElementById('conversionFactor');

    if (!smallUnitSelect || !largeUnitSelect || !conversionFactorInput) return;

    const smallUnitId = smallUnitSelect.value;
    const largeUnitId = largeUnitSelect.value;

    // If both units are selected and they're from the same unit system
    if (smallUnitId && largeUnitId && smallUnitId === largeUnitId) {
        const selectedOption = smallUnitSelect.options[smallUnitSelect.selectedIndex];
        const unitData = JSON.parse(selectedOption.dataset.unitData || '{}');

        if (unitData.conversionFactor) {
            conversionFactorInput.value = unitData.conversionFactor;
            conversionFactorInput.readOnly = true;

            // Show unit information
            showUnitInformation(unitData);
        }
    } else {
        conversionFactorInput.readOnly = false;
        hideUnitInformation();
    }
}

// Show unit information
function showUnitInformation(unitData) {
    // Remove existing unit info if any
    const existingInfo = document.getElementById('unitInformation');
    if (existingInfo) {
        existingInfo.remove();
    }

    // Create unit information display
    const conversionFactorInput = document.getElementById('conversionFactor');
    const infoDiv = document.createElement('div');
    infoDiv.id = 'unitInformation';
    infoDiv.className = 'mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-xs';
    infoDiv.innerHTML = `
        <div class="flex items-center mb-1">
            <span class="text-blue-600 ml-1">📏</span>
            <strong>معلومات وحدة القياس:</strong>
        </div>
        <div>• ${unitData.largeUnitCount || 1} ${unitData.largeUnitName} = ${unitData.smallUnitCount || unitData.conversionFactor} ${unitData.smallUnitName}</div>
        <div>• معامل التحويل: ${unitData.conversionFactor}</div>
        <div class="text-green-600 mt-1">✅ سيتم حساب الأسعار تلقائياً بناءً على سعر التكلفة</div>
    `;

    conversionFactorInput.parentNode.appendChild(infoDiv);
}

// Hide unit information
function hideUnitInformation() {
    const existingInfo = document.getElementById('unitInformation');
    if (existingInfo) {
        existingInfo.remove();
    }
}

// Calculate prices from cost price
function calculatePricesFromCostPrice() {
    const costPrice = parseFloat(document.getElementById('costPrice').value) || 0;
    const conversionFactor = parseFloat(document.getElementById('conversionFactor').value) || 1;
    const smallUnitPriceInput = document.getElementById('smallUnitPrice');
    const largeUnitPriceInput = document.getElementById('largeUnitPrice');

    if (!costPrice || !smallUnitPriceInput || !largeUnitPriceInput) return;

    // Determine which unit the cost price refers to
    const smallUnitSelect = document.getElementById('smallUnit');
    const largeUnitSelect = document.getElementById('largeUnit');

    if (smallUnitSelect.value && largeUnitSelect.value && smallUnitSelect.value === largeUnitSelect.value) {
        // Same unit system - cost price is for large unit
        largeUnitPriceInput.value = costPrice.toFixed(2);
        const smallUnitPrice = costPrice / conversionFactor;
        smallUnitPriceInput.value = smallUnitPrice.toFixed(4);

        // Update the display to show which unit the cost price refers to
        updateCostPriceLabel('large');
    } else {
        // Different units or not selected - assume cost price is for small unit
        smallUnitPriceInput.value = costPrice.toFixed(2);
        const largeUnitPrice = costPrice * conversionFactor;
        largeUnitPriceInput.value = largeUnitPrice.toFixed(2);

        updateCostPriceLabel('small');
    }
}

// Update cost price label to show which unit it refers to
function updateCostPriceLabel(unitType) {
    const costPriceLabel = document.querySelector('label[for="costPrice"]');
    if (!costPriceLabel) return;

    const smallUnitSelect = document.getElementById('smallUnit');
    const largeUnitSelect = document.getElementById('largeUnit');

    let unitName = '';
    if (unitType === 'large' && largeUnitSelect.value) {
        const selectedOption = largeUnitSelect.options[largeUnitSelect.selectedIndex];
        unitName = selectedOption.dataset.unitName || '';
    } else if (unitType === 'small' && smallUnitSelect.value) {
        const selectedOption = smallUnitSelect.options[smallUnitSelect.selectedIndex];
        unitName = selectedOption.dataset.unitName || '';
    }

    if (unitName) {
        costPriceLabel.innerHTML = `سعر التكلفة (${unitName}) *`;
    } else {
        costPriceLabel.innerHTML = 'سعر التكلفة *';
    }
}

// Populate all warehouse selects (no branch dependency)
function populateAllWarehouses() {
    const savedWarehouses = localStorage.getItem('anwar_bakery_warehouses');
    let warehouses = [];

    if (savedWarehouses) {
        warehouses = JSON.parse(savedWarehouses);
    }

    // Populate all warehouse selects
    const warehouseSelects = document.querySelectorAll('.warehouse-select, .opening-warehouse-select');
    warehouseSelects.forEach(select => {
        select.innerHTML = '<option value="">اختر المخزن</option>';

        warehouses.filter(warehouse => warehouse.isActive).forEach(warehouse => {
            const option = document.createElement('option');
            option.value = warehouse.id;
            // Support both name formats from different warehouse systems
            option.textContent = warehouse.name || warehouse.warehouseName || `مخزن ${warehouse.id}`;
            select.appendChild(option);
        });
    });
}

// Calculate large unit price based on small unit price and conversion factor
function calculateLargeUnitPrice() {
    const smallUnitPrice = parseFloat(document.getElementById('smallUnitPrice').value) || 0;
    const conversionFactor = parseFloat(document.getElementById('conversionFactor').value) || 1;

    const largeUnitPrice = smallUnitPrice * conversionFactor;
    document.getElementById('largeUnitPrice').value = largeUnitPrice.toFixed(2);
}

// Calculate small unit price based on large unit price and conversion factor
function calculateSmallUnitPrice() {
    const largeUnitPrice = parseFloat(document.getElementById('largeUnitPrice').value) || 0;
    const conversionFactor = parseFloat(document.getElementById('conversionFactor').value) || 1;

    if (conversionFactor > 0) {
        const smallUnitPrice = largeUnitPrice / conversionFactor;
        document.getElementById('smallUnitPrice').value = smallUnitPrice.toFixed(2);
    }
}

// Add warehouse row for multi-warehouse distribution
function addWarehouseRow() {
    const container = document.getElementById('warehousesContainer');
    const rowId = Date.now();

    const row = document.createElement('div');
    row.className = 'warehouse-row grid grid-cols-4 gap-2 mb-2 p-2 bg-white border border-gray-200 rounded';
    row.id = `warehouse-row-${rowId}`;

    row.innerHTML = `
        <div>
            <select class="warehouse-select w-full px-2 py-1 border border-gray-300 rounded text-sm" onchange="updateWarehouseInfo(${rowId})">
                <option value="">اختر المخزن</option>
                <!-- Warehouses will be populated -->
            </select>
        </div>
        <div>
            <input type="number" class="warehouse-min-stock w-full px-2 py-1 border border-gray-300 rounded text-sm"
                   placeholder="الحد الأدنى" min="0" step="0.01">
        </div>
        <div>
            <input type="number" class="warehouse-max-stock w-full px-2 py-1 border border-gray-300 rounded text-sm"
                   placeholder="الحد الأقصى" min="0" step="0.01">
        </div>
        <div class="flex items-center">
            <label class="flex items-center text-xs">
                <input type="checkbox" class="warehouse-primary mr-1" onchange="setPrimaryWarehouse(${rowId})">
                مخزن رئيسي
            </label>
            <button type="button" onclick="removeWarehouseRow(${rowId})" class="text-red-600 hover:text-red-900 text-xs mr-2">
                🗑️
            </button>
        </div>
    `;

    container.appendChild(row);
    populateWarehouseSelect(rowId);
}

// Remove warehouse row
function removeWarehouseRow(rowId) {
    const row = document.getElementById(`warehouse-row-${rowId}`);
    if (row) {
        row.remove();
    }
}

// Populate warehouse select in a specific row
function populateWarehouseSelect(rowId) {
    const savedWarehouses = localStorage.getItem('anwar_bakery_warehouses');
    let warehouses = [];

    if (savedWarehouses) {
        warehouses = JSON.parse(savedWarehouses);
    }

    const warehouseSelect = document.querySelector(`#warehouse-row-${rowId} .warehouse-select`);
    if (warehouseSelect) {
        warehouseSelect.innerHTML = '<option value="">اختر المخزن</option>';

        warehouses.filter(warehouse => warehouse.isActive).forEach(warehouse => {
            const option = document.createElement('option');
            option.value = warehouse.id;
            // Support both name formats from different warehouse systems
            option.textContent = warehouse.name || warehouse.warehouseName || `مخزن ${warehouse.id}`;
            warehouseSelect.appendChild(option);
        });
    }
}

// Set primary warehouse (only one can be primary)
function setPrimaryWarehouse(rowId) {
    const allPrimaryCheckboxes = document.querySelectorAll('.warehouse-primary');
    allPrimaryCheckboxes.forEach(checkbox => {
        if (checkbox.closest('.warehouse-row').id !== `warehouse-row-${rowId}`) {
            checkbox.checked = false;
        }
    });
}

// Add opening balance row
function addOpeningBalanceRow() {
    const container = document.getElementById('openingBalancesContainer');
    const rowId = Date.now();

    const row = document.createElement('div');
    row.className = 'opening-balance-row grid grid-cols-5 gap-2 mb-2 p-2 bg-white border border-gray-200 rounded';
    row.id = `opening-balance-row-${rowId}`;

    row.innerHTML = `
        <div>
            <select class="opening-warehouse-select w-full px-2 py-1 border border-gray-300 rounded text-sm">
                <option value="">اختر المخزن</option>
                <!-- Warehouses will be populated -->
            </select>
        </div>
        <div>
            <input type="number" class="opening-quantity w-full px-2 py-1 border border-gray-300 rounded text-sm"
                   placeholder="الكمية" min="0" step="0.01" onchange="calculateOpeningRowValue(${rowId})">
        </div>
        <div>
            <input type="number" class="opening-cost w-full px-2 py-1 border border-gray-300 rounded text-sm"
                   placeholder="سعر التكلفة" min="0" step="0.01" onchange="calculateOpeningRowValue(${rowId})">
        </div>
        <div>
            <input type="number" class="opening-value w-full px-2 py-1 border border-gray-300 rounded text-sm bg-gray-50"
                   placeholder="القيمة" readonly>
        </div>
        <div class="flex items-center">
            <button type="button" onclick="removeOpeningBalanceRow(${rowId})" class="text-red-600 hover:text-red-900 text-xs">
                🗑️ حذف
            </button>
        </div>
    `;

    container.appendChild(row);
    populateOpeningWarehouseSelect(rowId);
}

// Remove opening balance row
function removeOpeningBalanceRow(rowId) {
    const row = document.getElementById(`opening-balance-row-${rowId}`);
    if (row) {
        row.remove();
        calculateOpeningTotals();
    }
}

// Populate warehouse select in opening balance row (no branch dependency)
function populateOpeningWarehouseSelect(rowId) {
    const savedWarehouses = localStorage.getItem('anwar_bakery_warehouses');
    let warehouses = [];

    if (savedWarehouses) {
        warehouses = JSON.parse(savedWarehouses);
    }

    const warehouseSelect = document.querySelector(`#opening-balance-row-${rowId} .opening-warehouse-select`);
    if (warehouseSelect) {
        warehouseSelect.innerHTML = '<option value="">اختر المخزن</option>';

        const activeWarehouses = warehouses.filter(warehouse => warehouse.isActive);

        activeWarehouses.forEach(warehouse => {
            const option = document.createElement('option');
            option.value = warehouse.id;
            // Support both name formats from different warehouse systems
            option.textContent = warehouse.name || warehouse.warehouseName || `مخزن ${warehouse.id}`;
            warehouseSelect.appendChild(option);
        });
    }
}

// Calculate opening balance row value
function calculateOpeningRowValue(rowId) {
    const row = document.getElementById(`opening-balance-row-${rowId}`);
    const quantity = parseFloat(row.querySelector('.opening-quantity').value) || 0;
    const cost = parseFloat(row.querySelector('.opening-cost').value) || 0;
    const value = quantity * cost;

    row.querySelector('.opening-value').value = value.toFixed(2);
    calculateOpeningTotals();
}

// Calculate opening balance totals
function calculateOpeningTotals() {
    const rows = document.querySelectorAll('.opening-balance-row');
    let totalQuantity = 0;
    let totalValue = 0;

    rows.forEach(row => {
        const quantity = parseFloat(row.querySelector('.opening-quantity').value) || 0;
        const value = parseFloat(row.querySelector('.opening-value').value) || 0;

        totalQuantity += quantity;
        totalValue += value;
    });

    const averageCost = totalQuantity > 0 ? totalValue / totalQuantity : 0;

    document.getElementById('totalOpeningQuantity').value = totalQuantity.toFixed(2);
    document.getElementById('totalOpeningValue').value = totalValue.toFixed(2);
    document.getElementById('averageOpeningCost').value = averageCost.toFixed(2);
}

// Save items to localStorage
function saveItems() {
    localStorage.setItem('anwar_bakery_items', JSON.stringify(items));
}

// Update statistics
function updateStats() {
    const totalItems = items.length;
    const rawMaterials = items.filter(item => item.itemType === 'raw_material').length;
    const finishedProducts = items.filter(item => item.itemType === 'finished_product').length;
    const servicesCount = items.filter(item => item.itemType === 'service').length;

    document.getElementById('totalItems').textContent = totalItems;
    document.getElementById('rawMaterials').textContent = rawMaterials;
    document.getElementById('finishedProducts').textContent = finishedProducts;
    document.getElementById('servicesCount').textContent = servicesCount;
}

// Render items table
function renderItems(filteredItems = items) {
    const tbody = document.getElementById('itemsTableBody');
    tbody.innerHTML = '';

    filteredItems.forEach(item => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';

        const typeNames = {
            'raw_material': 'خامة',
            'finished_product': 'منتج نهائي',
            'semi_finished': 'نصف مصنع',
            'service': 'خدمة'
        };

        // Get unit names from units system
        const getUnitName = (unitId) => {
            if (item.itemType === 'service') return '-';
            const savedUnits = localStorage.getItem('anwar_bakery_units');
            if (savedUnits) {
                const units = JSON.parse(savedUnits);
                const unit = units.find(u => u.id == unitId);
                return unit ? unit.unitName : 'غير محدد';
            }
            return 'غير محدد';
        };

        // Get warehouse name
        const getWarehouseName = (warehouseId) => {
            if (item.itemType === 'service') return '-';
            const savedWarehouses = localStorage.getItem('anwar_bakery_warehouses');
            if (savedWarehouses) {
                const warehouses = JSON.parse(savedWarehouses);
                const warehouse = warehouses.find(w => w.id == warehouseId);
                return warehouse ? warehouse.warehouseName : 'غير محدد';
            }
            return 'غير محدد';
        };

        const isLowStock = item.totalCurrentStock <= item.minStock;
        const statusClass = item.isActive ?
            (isLowStock ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800') :
            'bg-gray-100 text-gray-800';
        const statusText = item.isActive ?
            (isLowStock ? 'نفاد مخزون' : 'نشط') :
            'غير نشط';

        // عرض المخازن
        const warehousesDisplay = item.warehouseStocks && item.warehouseStocks.length > 0
            ? item.warehouseStocks.map(ws => `${ws.warehouseName}: ${ws.currentStock}`).join('<br>')
            : 'لا توجد مخازن';

        row.innerHTML = `
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">${item.itemCode}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                <div class="font-medium">${item.itemName}</div>
                <div class="text-xs text-gray-500">${item.description || ''}</div>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                <span class="px-2 py-1 text-xs font-medium rounded-full ${
                    item.itemType === 'raw_material' ? 'bg-yellow-100 text-yellow-800' :
                    item.itemType === 'finished_product' ? 'bg-green-100 text-green-800' :
                    item.itemType === 'service' ? 'bg-blue-100 text-blue-800' :
                    'bg-gray-100 text-gray-800'
                }">
                    ${typeNames[item.itemType]}
                </span>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                <div class="text-xs">${item.itemType === 'service' ? '-' : warehousesDisplay}</div>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                ${item.itemType === 'service' ? '-' : getUnitName(item.smallUnit || item.unit)}
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                ${item.itemType === 'service' ? '-' : (item.largeUnit ? getUnitName(item.largeUnit) : '-')}
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">${item.costPrice.toFixed(2)} ${item.currency}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">${item.sellingPrice.toFixed(2)} ${item.currency}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 ${isLowStock ? 'text-red-600 font-semibold' : ''}">
                ${item.itemType === 'service' ? '-' : (item.totalCurrentStock || 0)}
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                ${item.itemType === 'service' ? '-' : (item.totalOpeningQuantity || 0)}
            </td>
            <td class="px-4 py-3 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">
                    ${statusText}
                </span>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-center">
                <div class="flex justify-center space-x-1">
                    <button onclick="editItem(${item.id})" class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50" title="تعديل">
                        ✏️
                    </button>
                    ${item.itemType !== 'service' ? `
                        <button onclick="showStockDetails(${item.id})" class="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50" title="تفاصيل المخزون">
                            📊
                        </button>
                        <button onclick="showDamageModal(${item.id})" class="text-orange-600 hover:text-orange-900 p-1 rounded hover:bg-orange-50" title="قيد إتلاف">
                            🗑️
                        </button>
                    ` : ''}
                    ${item.itemType === 'service' ? `
                        <button onclick="showServiceDetails(${item.id})" class="text-purple-600 hover:text-purple-900 p-1 rounded hover:bg-purple-50" title="تفاصيل الخدمة">
                            🛠️
                        </button>
                    ` : ''}
                    <button onclick="toggleItemStatus(${item.id})" class="text-yellow-600 hover:text-yellow-900 p-1 rounded hover:bg-yellow-50" title="${item.isActive ? 'إلغاء تفعيل' : 'تفعيل'}">
                        ${item.isActive ? '⏸️' : '▶️'}
                    </button>
                    <button onclick="deleteItem(${item.id})" class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50" title="حذف">
                        🗑️
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Filter items
function filterItems() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const categoryFilter = document.getElementById('categoryFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;

    const filtered = items.filter(item => {
        const matchesSearch = item.itemName.toLowerCase().includes(searchTerm) ||
                            item.itemCode.toLowerCase().includes(searchTerm);
        const matchesCategory = !categoryFilter || item.itemType === categoryFilter;
        let matchesStatus = true;

        if (statusFilter === 'active') {
            matchesStatus = item.isActive;
        } else if (statusFilter === 'inactive') {
            matchesStatus = !item.isActive;
        } else if (statusFilter === 'low_stock') {
            matchesStatus = item.currentStock <= item.minStock;
        }

        return matchesSearch && matchesCategory && matchesStatus;
    });

    renderItems(filtered);
}

// Toggle sidebar
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobileOverlay');

    if (sidebar.classList.contains('translate-x-full')) {
        sidebar.classList.remove('translate-x-full');
        overlay.classList.remove('hidden');
    } else {
        sidebar.classList.add('translate-x-full');
        overlay.classList.add('hidden');
    }
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_session');
        sessionStorage.removeItem('anwar_bakery_session');
        window.location.href = 'login.html';
    }
}

// Modal functions
function openAddModal() {
    document.getElementById('modalTitle').textContent = 'إضافة صنف جديد';
    document.getElementById('itemForm').reset();
    document.getElementById('itemId').value = '';
    document.getElementById('isActive').checked = true;
    document.getElementById('openingDate').value = new Date().toISOString().split('T')[0];

    // Load warehouses into dropdown
    populateWarehouses();
    populateAllWarehouses();

    switchTab('basic');
    document.getElementById('itemModal').classList.add('active');
}

function editItem(id) {
    const item = items.find(i => i.id === id);
    if (!item) {
        showMessage('الصنف غير موجود', 'error');
        return;
    }

    try {
        // Set modal title and item ID
        document.getElementById('modalTitle').textContent = 'تعديل الصنف';
        document.getElementById('itemId').value = item.id;

        // Basic information
        document.getElementById('itemCode').value = item.itemCode || '';
        document.getElementById('barcode').value = item.barcode || '';
        document.getElementById('itemName').value = item.itemName || '';
        document.getElementById('itemType').value = item.itemType || '';
        document.getElementById('category').value = item.category || '';
        document.getElementById('description').value = item.description || '';
        document.getElementById('costPrice').value = item.costPrice || 0;
        document.getElementById('sellingPrice').value = item.sellingPrice || 0;
        document.getElementById('currency').value = item.currency || 'SAR';

        // Units and inventory
        document.getElementById('minStock').value = item.minStock || 0;
        document.getElementById('maxStock').value = item.maxStock || 0;
        document.getElementById('shelfLife').value = item.shelfLife || '';
        document.getElementById('storageLocation').value = item.storageLocation || '';

        // Branch and warehouse
        populateBranches();
        setTimeout(() => {
            document.getElementById('branchId').value = item.branchId || '';
            populateWarehouses();
            setTimeout(() => {
                document.getElementById('warehouseId').value = item.warehouseId || '';
            }, 100);
        }, 100);

        // Units data
        populateUnits();
        setTimeout(() => {
            if (item.unitsData) {
                document.getElementById('smallUnit').value = item.unitsData.smallUnit || '';
                document.getElementById('largeUnit').value = item.unitsData.largeUnit || '';
                document.getElementById('conversionFactor').value = item.unitsData.conversionFactor || 1;
                document.getElementById('smallUnitPrice').value = item.unitsData.smallUnitPrice || 0;
                document.getElementById('largeUnitPrice').value = item.unitsData.largeUnitPrice || 0;
            }
            // Backward compatibility
            if (item.unit) {
                document.getElementById('unit').value = item.unit;
            }
        }, 100);

        // Status and opening balance
        document.getElementById('isActive').checked = item.isActive !== false;
        document.getElementById('openingQuantity').value = item.openingQuantity || 0;
        document.getElementById('openingValue').value = item.openingValue || 0;
        document.getElementById('openingDate').value = item.openingDate || new Date().toISOString().split('T')[0];
        document.getElementById('openingNotes').value = item.openingNotes || '';

        // Accounting integration
        if (item.accountingIntegration) {
            document.getElementById('inventoryAccount').value = item.accountingIntegration.inventoryAccount || '';
            document.getElementById('purchaseAccount').value = item.accountingIntegration.purchaseAccount || '';
            document.getElementById('cogsAccount').value = item.accountingIntegration.cogsAccount || '';
            document.getElementById('salesAccount').value = item.accountingIntegration.salesAccount || '';
            document.getElementById('customInventoryAccount').value = item.accountingIntegration.customInventoryAccount || '';
            document.getElementById('customCogsAccount').value = item.accountingIntegration.customCogsAccount || '';
            document.getElementById('autoCreateJournalEntry').checked = item.accountingIntegration.autoCreateJournalEntry !== false;
            document.getElementById('autoUpdateAccounts').checked = item.accountingIntegration.autoUpdateAccounts !== false;
            document.getElementById('validateAccountMapping').checked = item.accountingIntegration.validateAccountMapping || false;
        }

        // Service-specific fields
        if (item.itemType === 'service') {
            document.getElementById('serviceDuration').value = item.serviceDuration || 0;
            document.getElementById('serviceDurationUnit').value = item.serviceDurationUnit || 'hours';
            document.getElementById('servicePricingType').value = item.servicePricingType || 'fixed';
            document.getElementById('serviceLaborCost').value = item.serviceLaborCost || 0;
            document.getElementById('serviceMinOrder').value = item.serviceMinOrder || 0;
            document.getElementById('serviceRequiresBooking').checked = item.serviceRequiresBooking || false;
            document.getElementById('serviceAvailableOnline').checked = item.serviceAvailableOnline || false;
            document.getElementById('serviceRequirements').value = item.serviceRequirements || '';
            document.getElementById('serviceRequiredMaterials').value = item.serviceRequiredMaterials || '';
        }

        // Load warehouse distribution if exists
        if (item.warehouseDistribution && item.warehouseDistribution.length > 0) {
            // Clear existing warehouse rows
            document.getElementById('warehousesContainer').innerHTML = '';

            item.warehouseDistribution.forEach(warehouse => {
                addWarehouseRow();
                const lastRow = document.querySelector('.warehouse-row:last-child');
                if (lastRow) {
                    lastRow.querySelector('.warehouse-select').value = warehouse.warehouseId;
                    lastRow.querySelector('.warehouse-min-stock').value = warehouse.minStock;
                    lastRow.querySelector('.warehouse-max-stock').value = warehouse.maxStock;
                    lastRow.querySelector('.warehouse-primary').checked = warehouse.isPrimary;
                }
            });
        }

        // Load opening balances if exists
        if (item.openingBalances && item.openingBalances.length > 0) {
            // Clear existing opening balance rows
            document.getElementById('openingBalancesContainer').innerHTML = '';

            item.openingBalances.forEach(balance => {
                addOpeningBalanceRow();
                const lastRow = document.querySelector('.opening-balance-row:last-child');
                if (lastRow) {
                    lastRow.querySelector('.opening-warehouse-select').value = balance.warehouseId;
                    lastRow.querySelector('.opening-quantity').value = balance.quantity;
                    lastRow.querySelector('.opening-cost').value = balance.costPrice;
                    lastRow.querySelector('.opening-value').value = balance.totalValue;
                }
            });
            calculateOpeningTotals();
        }

        // Toggle service fields based on item type
        toggleServiceFields();

        // Load warehouses into dropdown
        populateWarehouses();
        populateAllWarehouses();

        // Trigger smart accounting mapping if enabled
        setTimeout(() => {
            if (document.getElementById('autoUpdateAccounts').checked) {
                performSmartAccountMapping();
            }
        }, 200);

        // Calculate values
        calculateProfitMargin();
        // calculateOpeningValue(); // This function doesn't exist, removing it
        calculatePricesFromCostPrice();

        // Switch to basic tab and show modal
        switchTab('basic');
        document.getElementById('itemModal').classList.add('active');

    } catch (error) {
        console.error('Error loading item for edit:', error);
        showMessage('خطأ في تحميل بيانات الصنف', 'error');
    }
}

function closeModal() {
    document.getElementById('itemModal').classList.remove('active');
}

// Tab switching for modal tabs
function switchTab(tabName, event) {
    try {
        // Prevent default if event exists
        if (event) {
            event.preventDefault();
        }

        // Hide all modal tab contents
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });

        // Remove active class from all modal tab buttons
        document.querySelectorAll('.modal-tab-button').forEach(button => {
            button.classList.remove('border-blue-500', 'text-blue-600');
            button.classList.add('border-transparent', 'text-gray-500');
        });

        // Show selected tab content
        const targetTab = document.getElementById(tabName + 'Tab');
        if (targetTab) {
            targetTab.classList.add('active');
        } else {
            console.warn(`Tab content with ID '${tabName}Tab' not found`);
        }

        // Activate selected tab button
        if (event && event.target) {
            event.target.classList.remove('border-transparent', 'text-gray-500');
            event.target.classList.add('border-blue-500', 'text-blue-600');
        } else {
            // Find the button by onclick attribute if event is not available
            const tabButton = document.querySelector(`button[onclick*="switchTab('${tabName}')"]`);
            if (tabButton) {
                tabButton.classList.remove('border-transparent', 'text-gray-500');
                tabButton.classList.add('border-blue-500', 'text-blue-600');
            }
        }

        // Special handling for service tab visibility
        if (tabName === 'service') {
            const itemType = document.getElementById('itemType').value;
            if (itemType !== 'service') {
                // If not a service, switch back to basic tab
                switchTab('basic');
                return;
            }
        }

        console.log(`Switched to tab: ${tabName}`);
    } catch (error) {
        console.error('Error switching tabs:', error);
    }
}

// Save item
function saveItem() {
    const form = document.getElementById('itemForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const itemId = document.getElementById('itemId').value;
    const itemData = {
        itemCode: document.getElementById('itemCode').value,
        barcode: document.getElementById('barcode').value,
        itemName: document.getElementById('itemName').value,
        itemType: document.getElementById('itemType').value,
        category: document.getElementById('category').value,
        description: document.getElementById('description').value,
        costPrice: parseFloat(document.getElementById('costPrice').value),
        sellingPrice: parseFloat(document.getElementById('sellingPrice').value),
        currency: document.getElementById('currency').value,
        unit: document.getElementById('unit').value,
        minStock: parseFloat(document.getElementById('minStock').value) || 0,
        maxStock: parseFloat(document.getElementById('maxStock').value) || 0,
        shelfLife: parseInt(document.getElementById('shelfLife').value) || 0,
        storageLocation: document.getElementById('storageLocation').value,
        branchId: parseInt(document.getElementById('branchId').value) || null,
        branchName: getBranchName(document.getElementById('branchId').value),
        warehouseId: parseInt(document.getElementById('warehouseId').value) || null,
        warehouseName: getWarehouseName(document.getElementById('warehouseId').value),
        isActive: document.getElementById('isActive').checked,
        openingQuantity: parseFloat(document.getElementById('openingQuantity').value) || 0,
        openingValue: parseFloat(document.getElementById('openingValue').value) || 0,
        openingDate: document.getElementById('openingDate').value,
        openingNotes: document.getElementById('openingNotes').value
    };

    // Add service-specific fields if it's a service
    if (itemData.itemType === 'service') {
        itemData.serviceDuration = parseFloat(document.getElementById('serviceDuration').value) || 0;
        itemData.serviceDurationUnit = document.getElementById('serviceDurationUnit').value;
        itemData.servicePricingType = document.getElementById('servicePricingType').value;
        itemData.serviceLaborCost = parseFloat(document.getElementById('serviceLaborCost').value) || 0;
        itemData.serviceMinOrder = parseFloat(document.getElementById('serviceMinOrder').value) || 0;
        itemData.serviceRequiresBooking = document.getElementById('serviceRequiresBooking').checked;
        itemData.serviceAvailableOnline = document.getElementById('serviceAvailableOnline').checked;
        itemData.serviceRequirements = document.getElementById('serviceRequirements').value;
        itemData.serviceRequiredMaterials = document.getElementById('serviceRequiredMaterials').value;

        // Services don't need inventory fields
        itemData.unit = '';
        itemData.minStock = 0;
        itemData.maxStock = 0;
        itemData.currentStock = 0;
        itemData.openingQuantity = 0;
        itemData.openingValue = 0;
    }

    // Add smart accounting integration data
    itemData.accountingIntegration = {
        inventoryAccount: document.getElementById('inventoryAccount').value,
        purchaseAccount: document.getElementById('purchaseAccount').value,
        cogsAccount: document.getElementById('cogsAccount').value,
        salesAccount: document.getElementById('salesAccount').value,
        customInventoryAccount: document.getElementById('customInventoryAccount').value,
        customCogsAccount: document.getElementById('customCogsAccount').value,
        autoCreateJournalEntry: document.getElementById('autoCreateJournalEntry').checked,
        autoUpdateAccounts: document.getElementById('autoUpdateAccounts').checked,
        validateAccountMapping: document.getElementById('validateAccountMapping').checked,
        mappingRules: accountMappingRules[itemData.itemType] || null,
        lastMappingUpdate: new Date().toISOString()
    };

    // Add units data
    itemData.unitsData = {
        smallUnit: document.getElementById('smallUnit').value,
        largeUnit: document.getElementById('largeUnit').value,
        conversionFactor: parseFloat(document.getElementById('conversionFactor').value) || 1,
        smallUnitPrice: parseFloat(document.getElementById('smallUnitPrice').value) || 0,
        largeUnitPrice: parseFloat(document.getElementById('largeUnitPrice').value) || 0
    };

    // Add warehouse distribution data
    const warehouseRows = document.querySelectorAll('.warehouse-row');
    itemData.warehouseDistribution = [];
    warehouseRows.forEach(row => {
        const warehouseId = row.querySelector('.warehouse-select').value;
        const minStock = parseFloat(row.querySelector('.warehouse-min-stock').value) || 0;
        const maxStock = parseFloat(row.querySelector('.warehouse-max-stock').value) || 0;
        const isPrimary = row.querySelector('.warehouse-primary').checked;

        if (warehouseId) {
            itemData.warehouseDistribution.push({
                warehouseId: parseInt(warehouseId),
                warehouseName: getWarehouseName(warehouseId),
                minStock: minStock,
                maxStock: maxStock,
                isPrimary: isPrimary,
                currentStock: 0 // Will be updated through inventory movements
            });
        }
    });

    // Add opening balances data
    const openingBalanceRows = document.querySelectorAll('.opening-balance-row');
    itemData.openingBalances = [];
    openingBalanceRows.forEach(row => {
        const warehouseId = row.querySelector('.opening-warehouse-select').value;
        const quantity = parseFloat(row.querySelector('.opening-quantity').value) || 0;
        const cost = parseFloat(row.querySelector('.opening-cost').value) || 0;
        const value = parseFloat(row.querySelector('.opening-value').value) || 0;

        if (warehouseId && quantity > 0) {
            itemData.openingBalances.push({
                warehouseId: parseInt(warehouseId),
                warehouseName: getWarehouseName(warehouseId),
                quantity: quantity,
                costPrice: cost,
                totalValue: value,
                date: document.getElementById('openingDate').value || new Date().toISOString().split('T')[0]
            });
        }
    });

    // Calculate totals from opening balances
    itemData.totalOpeningQuantity = itemData.openingBalances.reduce((sum, balance) => sum + balance.quantity, 0);
    itemData.totalOpeningValue = itemData.openingBalances.reduce((sum, balance) => sum + balance.totalValue, 0);
    itemData.averageOpeningCost = itemData.totalOpeningQuantity > 0 ? itemData.totalOpeningValue / itemData.totalOpeningQuantity : 0;

    // Validate account mapping if enabled
    if (document.getElementById('validateAccountMapping').checked) {
        const validationResult = validateAccountMappingData(itemData);
        if (!validationResult.isValid) {
            showMessage(`خطأ في الربط المحاسبي: ${validationResult.message}`, 'error');
            return;
        }
    }

    // Check if item code already exists
    const existingItem = items.find(i => i.itemCode === itemData.itemCode && i.id != itemId);
    if (existingItem) {
        showMessage('كود الصنف موجود مسبقاً!', 'error');
        return;
    }

    if (itemId) {
        // Edit existing item
        const itemIndex = items.findIndex(i => i.id == itemId);
        if (itemIndex !== -1) {
            items[itemIndex] = {
                ...items[itemIndex],
                ...itemData,
                currentStock: itemData.openingQuantity || items[itemIndex].currentStock
            };
        }
    } else {
        // Add new item
        const newItem = {
            id: Math.max(...items.map(i => i.id), 0) + 1,
            ...itemData,
            currentStock: itemData.openingQuantity || 0,
            createdAt: new Date().toISOString().split('T')[0]
        };
        items.push(newItem);
    }

    // Create journal entry for opening balance if enabled
    let journalEntry = null;
    if (itemData.totalOpeningValue > 0 && itemData.accountingIntegration.autoCreateJournalEntry) {
        journalEntry = createOpeningBalanceJournalEntry(itemData);
    }

    saveItems();
    renderItems();
    updateStats();
    closeModal();

    // Show success message with accounting integration summary
    let successMessage = 'تم حفظ الصنف بنجاح!';
    if (journalEntry) {
        successMessage += `\n\n✅ تم إنشاء قيد محاسبي للرصيد الافتتاحي برقم: ${journalEntry.entryNumber}`;
    }

    // Show accounting summary if there's integration data
    if (itemData.accountingIntegration && (itemData.accountingIntegration.inventoryAccount || itemData.accountingIntegration.salesAccount)) {
        const summary = showAccountingIntegrationSummary(itemData);
        if (summary) {
            setTimeout(() => {
                if (confirm('هل تريد عرض ملخص الربط المحاسبي؟')) {
                    alert(summary);
                }
            }, 1000);
        }
    }

    showMessage(successMessage, 'success');
}

// Toggle item status
function toggleItemStatus(id) {
    const item = items.find(i => i.id === id);
    if (item) {
        item.isActive = !item.isActive;
        saveItems();
        renderItems();
        updateStats();
        showMessage(`تم ${item.isActive ? 'تفعيل' : 'إلغاء تفعيل'} الصنف بنجاح!`, 'success');
    }
}

// Delete item
function deleteItem(id) {
    if (confirm('هل أنت متأكد من حذف هذا الصنف؟')) {
        items = items.filter(i => i.id !== id);
        saveItems();
        renderItems();
        updateStats();
        showMessage('تم حذف الصنف بنجاح!', 'success');
    }
}

// Opening balance modal functions
function openOpeningBalanceModal() {
    renderOpeningBalanceTable();
    document.getElementById('openingBalanceModal').classList.add('active');
}

function closeOpeningBalanceModal() {
    document.getElementById('openingBalanceModal').classList.remove('active');
}

function renderOpeningBalanceTable() {
    const tbody = document.getElementById('openingBalanceTableBody');
    tbody.innerHTML = '';
    let totalValue = 0;

    items.forEach(item => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';

        const unitNames = {
            'kg': 'كيلوجرام',
            'g': 'جرام',
            'piece': 'قطعة',
            'box': 'صندوق',
            'bag': 'كيس',
            'liter': 'لتر',
            'ml': 'مليلتر'
        };

        const openingValue = (item.openingQuantity || 0) * (item.costPrice || 0);
        totalValue += openingValue;

        row.innerHTML = `
            <td class="px-4 py-3 text-sm font-medium text-gray-900">${item.itemCode}</td>
            <td class="px-4 py-3 text-sm text-gray-900">${item.itemName}</td>
            <td class="px-4 py-3 text-sm text-gray-900">${unitNames[item.unit]}</td>
            <td class="px-4 py-3">
                <input type="number" step="0.01" min="0" value="${item.openingQuantity || 0}"
                       class="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                       onchange="updateOpeningBalance(${item.id}, 'quantity', this.value)">
            </td>
            <td class="px-4 py-3">
                <input type="number" step="0.01" min="0" value="${item.costPrice || 0}"
                       class="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                       onchange="updateOpeningBalance(${item.id}, 'costPrice', this.value)">
            </td>
            <td class="px-4 py-3 text-sm font-semibold text-gray-900" id="value_${item.id}">
                ${openingValue.toFixed(2)}
            </td>
            <td class="px-4 py-3">
                <input type="date" value="${item.openingDate || ''}"
                       class="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                       onchange="updateOpeningBalance(${item.id}, 'date', this.value)">
            </td>
        `;
        tbody.appendChild(row);
    });

    document.getElementById('totalOpeningValue').textContent = totalValue.toFixed(2);
}

function updateOpeningBalance(itemId, field, value) {
    const item = items.find(i => i.id === itemId);
    if (item) {
        if (field === 'quantity') {
            item.openingQuantity = parseFloat(value) || 0;
            item.currentStock = item.openingQuantity;
        } else if (field === 'costPrice') {
            item.costPrice = parseFloat(value) || 0;
        } else if (field === 'date') {
            item.openingDate = value;
        }

        // Update opening value
        const openingValue = (item.openingQuantity || 0) * (item.costPrice || 0);
        item.openingValue = openingValue;
        document.getElementById(`value_${itemId}`).textContent = openingValue.toFixed(2);

        // Update total
        let totalValue = 0;
        items.forEach(i => {
            totalValue += (i.openingQuantity || 0) * (i.costPrice || 0);
        });
        document.getElementById('totalOpeningValue').textContent = totalValue.toFixed(2);
    }
}

function saveAllOpeningBalances() {
    saveItems();
    renderItems();
    updateStats();
    closeOpeningBalanceModal();
    showMessage('تم حفظ جميع الأرصدة الافتتاحية بنجاح!', 'success');
}

// Export to Excel (simplified)
function exportToExcel() {
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "كود الصنف,اسم الصنف,النوع,الوحدة,سعر التكلفة,سعر البيع,الكمية الحالية,الحد الأدنى,الحالة\n";

    items.forEach(item => {
        const typeNames = {
            'raw_material': 'خامة',
            'finished_product': 'منتج نهائي',
            'semi_finished': 'نصف مصنع'
        };

        const unitNames = {
            'kg': 'كيلوجرام',
            'g': 'جرام',
            'piece': 'قطعة',
            'box': 'صندوق',
            'bag': 'كيس',
            'liter': 'لتر',
            'ml': 'مليلتر'
        };

        const row = [
            item.itemCode,
            item.itemName,
            typeNames[item.itemType],
            unitNames[item.unit],
            item.costPrice,
            item.sellingPrice,
            item.currentStock,
            item.minStock,
            item.isActive ? 'نشط' : 'غير نشط'
        ].join(',');
        csvContent += row + "\n";
    });

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "items_export.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Unit cost calculation functions
function calculateUnitCost(unitId, purchasePrice) {
    const savedUnits = localStorage.getItem('anwar_bakery_units');
    if (!savedUnits) return null;

    const units = JSON.parse(savedUnits);
    const unit = units.find(u => u.id == unitId);

    if (!unit) return null;

    return {
        unitName: unit.unitName,
        largeUnitName: unit.largeUnitName,
        smallUnitName: unit.smallUnitName,
        conversionFactor: unit.conversionFactor,
        costPerLargeUnit: purchasePrice,
        costPerSmallUnit: purchasePrice / unit.conversionFactor,
        description: `${unit.largeUnitCount} ${unit.largeUnitName} = ${unit.smallUnitCount.toLocaleString()} ${unit.smallUnitName}`
    };
}

// Show unit cost calculator
function showUnitCostCalculator(itemId) {
    const item = items.find(i => i.id === itemId);
    if (!item || !item.unit) {
        alert('يجب تحديد وحدة القياس أولاً');
        return;
    }

    const costInfo = calculateUnitCost(item.unit, item.costPrice);
    if (!costInfo) {
        alert('وحدة القياس غير صحيحة');
        return;
    }

    let message = `حساب تكلفة ${item.itemName}:\n\n`;
    message += `وحدة القياس: ${costInfo.unitName}\n`;
    message += `${costInfo.description}\n\n`;
    message += `سعر الشراء: ${costInfo.costPerLargeUnit.toFixed(2)} ر.ي لكل ${costInfo.largeUnitName}\n`;
    message += `تكلفة ${costInfo.smallUnitName} الواحد: ${costInfo.costPerSmallUnit.toFixed(6)} ر.ي\n\n`;
    message += `مثال للاستخدام في الوصفات:\n`;
    message += `إذا كانت الوصفة تحتاج 500 ${costInfo.smallUnitName}\n`;
    message += `التكلفة = 500 × ${costInfo.costPerSmallUnit.toFixed(6)} = ${(500 * costInfo.costPerSmallUnit).toFixed(2)} ر.ي`;

    alert(message);
}

// Show service details
function showServiceDetails(itemId) {
    const item = items.find(i => i.id === itemId);
    if (!item || item.itemType !== 'service') {
        alert('هذا الصنف ليس خدمة');
        return;
    }

    const durationUnits = {
        'hours': 'ساعة',
        'days': 'يوم',
        'weeks': 'أسبوع'
    };

    const pricingTypes = {
        'fixed': 'سعر ثابت',
        'hourly': 'بالساعة',
        'daily': 'باليوم',
        'quantity': 'حسب الكمية'
    };

    let message = `تفاصيل الخدمة: ${item.itemName}\n\n`;
    message += `📋 الوصف: ${item.description}\n\n`;
    message += `⏱️ مدة التنفيذ: ${item.serviceDuration} ${durationUnits[item.serviceDurationUnit]}\n`;
    message += `💰 نوع التسعير: ${pricingTypes[item.servicePricingType]}\n`;
    message += `💵 سعر الخدمة: ${item.sellingPrice.toFixed(2)} ${item.currency}\n`;
    message += `👷 تكلفة العمالة: ${item.serviceLaborCost.toFixed(2)} ${item.currency}\n`;
    message += `📦 الحد الأدنى للطلب: ${item.serviceMinOrder}\n\n`;

    message += `📅 يتطلب حجز مسبق: ${item.serviceRequiresBooking ? 'نعم' : 'لا'}\n`;
    message += `🌐 متاح أونلاين: ${item.serviceAvailableOnline ? 'نعم' : 'لا'}\n\n`;

    if (item.serviceRequirements) {
        message += `📝 متطلبات خاصة:\n${item.serviceRequirements}\n\n`;
    }

    if (item.serviceRequiredMaterials) {
        message += `🛠️ المواد المطلوبة:\n${item.serviceRequiredMaterials}`;
    }

    alert(message);
}

// Populate branches from branches system
function populateBranches() {
    const savedBranches = localStorage.getItem('anwar_bakery_branches');
    let branches = [];

    if (savedBranches) {
        branches = JSON.parse(savedBranches);
    }

    const branchSelect = document.getElementById('branchId');
    if (branchSelect) {
        branchSelect.innerHTML = '<option value="">اختر الفرع</option>';

        branches.filter(branch => branch.isActive).forEach(branch => {
            const option = document.createElement('option');
            option.value = branch.id;
            option.textContent = branch.branchName;
            branchSelect.appendChild(option);
        });
    }
}

// Populate warehouses (no branch dependency)
function populateWarehouses() {
    const warehouseSelect = document.getElementById('warehouseId');

    if (!warehouseSelect) return;

    warehouseSelect.innerHTML = '<option value="">اختر المخزن</option>';

    const savedWarehouses = localStorage.getItem('anwar_bakery_warehouses');
    if (savedWarehouses) {
        const warehouses = JSON.parse(savedWarehouses);
        const activeWarehouses = warehouses.filter(warehouse => warehouse.isActive);

        activeWarehouses.forEach(warehouse => {
            const option = document.createElement('option');
            option.value = warehouse.id;
            // Support both name formats from different warehouse systems
            option.textContent = warehouse.name || warehouse.warehouseName || `مخزن ${warehouse.id}`;
            warehouseSelect.appendChild(option);
        });
    }
}

// Get branch name by ID
function getBranchName(branchId) {
    const savedBranches = localStorage.getItem('anwar_bakery_branches');
    if (savedBranches) {
        const branches = JSON.parse(savedBranches);
        const branch = branches.find(b => b.id == branchId);
        return branch ? branch.branchName : 'غير محدد';
    }
    return 'غير محدد';
}

// Get warehouse name by ID
function getWarehouseName(warehouseId) {
    const savedWarehouses = localStorage.getItem('anwar_bakery_warehouses');
    if (savedWarehouses) {
        const warehouses = JSON.parse(savedWarehouses);
        const warehouse = warehouses.find(w => w.id == warehouseId);
        return warehouse ? warehouse.warehouseName : 'غير محدد';
    }
    return 'غير محدد';
}

// Get currency symbol from settings
function getCurrencySymbol() {
    const savedSettings = localStorage.getItem('anwar_bakery_settings');
    if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        return settings.currency || 'ر.ي';
    }
    return 'ر.ي';
}

// Show stock details modal
function showStockDetails(itemId) {
    const item = items.find(i => i.id === itemId);
    if (!item) return;

    // Create modal HTML
    const modalHTML = `
        <div id="stockDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900 flex items-center">
                            <span class="text-2xl ml-2">📊</span>
                            تفاصيل مخزون: ${item.itemName}
                        </h3>
                        <button onclick="closeStockDetailsModal()" class="text-gray-400 hover:text-gray-600">
                            <span class="text-xl">×</span>
                        </button>
                    </div>

                    <!-- Item Summary -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">كود الصنف</label>
                                <div class="text-lg font-semibold text-blue-600">${item.itemCode}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">الكمية الحالية</label>
                                <div class="text-lg font-semibold ${item.currentStock <= item.minStock ? 'text-red-600' : 'text-green-600'}">
                                    ${item.currentStock}
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">الرصيد الافتتاحي</label>
                                <div class="text-lg font-semibold text-gray-600">${item.openingQuantity || 0}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">قيمة المخزون</label>
                                <div class="text-lg font-semibold text-purple-600">
                                    ${(item.currentStock * item.costPrice).toFixed(2)} ${item.currency}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Stock Levels -->
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                        <h4 class="text-md font-medium text-gray-900 mb-3">مستويات المخزون</h4>
                        <div class="grid grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">الحد الأدنى</label>
                                <div class="text-lg font-semibold text-orange-600">${item.minStock}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">الحد الأقصى</label>
                                <div class="text-lg font-semibold text-blue-600">${item.maxStock}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">نقطة إعادة الطلب</label>
                                <div class="text-lg font-semibold text-red-600">${Math.ceil(item.minStock * 1.2)}</div>
                            </div>
                        </div>

                        <!-- Stock Status Bar -->
                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">حالة المخزون</label>
                            <div class="w-full bg-gray-200 rounded-full h-4">
                                <div class="h-4 rounded-full ${
                                    item.currentStock <= item.minStock ? 'bg-red-500' :
                                    item.currentStock <= item.minStock * 1.5 ? 'bg-yellow-500' :
                                    'bg-green-500'
                                }" style="width: ${Math.min((item.currentStock / item.maxStock) * 100, 100)}%"></div>
                            </div>
                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                <span>0</span>
                                <span>الحد الأدنى: ${item.minStock}</span>
                                <span>الحد الأقصى: ${item.maxStock}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Warehouse Distribution -->
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                        <h4 class="text-md font-medium text-gray-900 mb-3">توزيع المخازن</h4>
                        <div class="text-sm text-gray-600">
                            <div class="flex justify-between items-center p-2 bg-white rounded border">
                                <span>${item.warehouseName || 'المخزن الرئيسي'}</span>
                                <span class="font-semibold">${item.currentStock}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Units Information -->
                    <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-4">
                        <h4 class="text-md font-medium text-gray-900 mb-3">معلومات الوحدات</h4>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">الوحدة الصغرى</label>
                                <div class="text-sm text-gray-600">${item.smallUnitName || 'غير محدد'}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">الوحدة الكبرى</label>
                                <div class="text-sm text-gray-600">${item.largeUnitName || 'غير محدد'}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">معامل التحويل</label>
                                <div class="text-sm text-gray-600">${item.conversionFactor || 1}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">سعر الوحدة الصغرى</label>
                                <div class="text-sm text-gray-600">${item.smallUnitPrice || item.costPrice} ${item.currency}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="mt-6 flex justify-end space-x-3">
                        <button onclick="closeStockDetailsModal()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                            إغلاق
                        </button>
                        <button onclick="editItem(${item.id}); closeStockDetailsModal();" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                            تعديل الصنف
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

// Close stock details modal
function closeStockDetailsModal() {
    const modal = document.getElementById('stockDetailsModal');
    if (modal) {
        modal.remove();
    }
}

// Smart Accounting Integration Functions

// Account mapping rules based on item type, branch, and warehouse
const accountMappingRules = {
    raw_material: {
        inventory: {
            pattern: 'مخزون الخامات',
            codes: ['1301', '1302', '1303'],
            description: 'حسابات مخزون المواد الخام'
        },
        purchase: {
            pattern: 'مشتريات الخامات',
            codes: ['5101', '5102', '5103'],
            description: 'حسابات مشتريات المواد الخام'
        },
        cogs: {
            pattern: 'تكلفة الخامات المستخدمة',
            codes: ['5201', '5202', '5203'],
            description: 'حسابات تكلفة المواد الخام المستخدمة'
        }
    },
    finished_product: {
        inventory: {
            pattern: 'مخزون المنتجات النهائية',
            codes: ['1311', '1312', '1313'],
            description: 'حسابات مخزون المنتجات النهائية'
        },
        sales: {
            pattern: 'مبيعات المنتجات',
            codes: ['4101', '4102', '4103'],
            description: 'حسابات مبيعات المنتجات'
        },
        cogs: {
            pattern: 'تكلفة البضاعة المباعة',
            codes: ['5301', '5302', '5303'],
            description: 'حسابات تكلفة البضاعة المباعة'
        }
    },
    semi_finished: {
        inventory: {
            pattern: 'مخزون المنتجات نصف المصنعة',
            codes: ['1321', '1322', '1323'],
            description: 'حسابات مخزون المنتجات نصف المصنعة'
        },
        production: {
            pattern: 'تكاليف الإنتاج',
            codes: ['5401', '5402', '5403'],
            description: 'حسابات تكاليف الإنتاج'
        }
    },
    service: {
        revenue: {
            pattern: 'إيرادات الخدمات',
            codes: ['4201', '4202', '4203'],
            description: 'حسابات إيرادات الخدمات'
        },
        expense: {
            pattern: 'مصروفات الخدمات',
            codes: ['5501', '5502', '5503'],
            description: 'حسابات مصروفات الخدمات'
        }
    }
};

// Branch-specific account suffixes
const branchAccountSuffixes = {
    1: '01', // الرياض الرئيسي
    2: '02', // جدة
    3: '03', // الدمام
    4: '04'  // المدينة المنورة
};

// Warehouse-specific account suffixes
const warehouseAccountSuffixes = {
    1: '1', // مخزن رئيسي
    2: '2', // مخزن فرعي
    3: '3', // مخزن التبريد
    4: '4'  // مخزن المواد الخام
};

// Smart account selection based on item data
function performSmartAccountMapping() {
    const itemType = document.getElementById('itemType').value;
    const branchId = document.getElementById('branchId').value;
    const warehouseId = document.getElementById('warehouseId').value;

    if (!itemType) {
        resetAccountSelections();
        return;
    }

    // Update account mapping rules display
    updateAccountMappingRulesDisplay(itemType, branchId, warehouseId);

    // Get suggested accounts
    const suggestedAccounts = getSuggestedAccounts(itemType, branchId, warehouseId);

    // Populate account dropdowns
    populateAccountDropdowns(suggestedAccounts);

    // Update status indicators
    updateAccountStatusIndicators(suggestedAccounts);
}

// Get suggested accounts based on smart mapping
function getSuggestedAccounts(itemType, branchId, warehouseId) {
    const accounts = loadChartOfAccounts();
    const rules = accountMappingRules[itemType];
    const branchSuffix = branchAccountSuffixes[branchId] || '01';
    const warehouseSuffix = warehouseAccountSuffixes[warehouseId] || '1';

    const suggestions = {};

    if (rules) {
        // Find inventory account
        if (rules.inventory) {
            suggestions.inventory = findBestMatchingAccount(
                accounts,
                rules.inventory,
                branchSuffix,
                warehouseSuffix
            );
        }

        // Find purchase account
        if (rules.purchase) {
            suggestions.purchase = findBestMatchingAccount(
                accounts,
                rules.purchase,
                branchSuffix,
                warehouseSuffix
            );
        }

        // Find COGS account
        if (rules.cogs) {
            suggestions.cogs = findBestMatchingAccount(
                accounts,
                rules.cogs,
                branchSuffix,
                warehouseSuffix
            );
        }

        // Find sales account
        if (rules.sales) {
            suggestions.sales = findBestMatchingAccount(
                accounts,
                rules.sales,
                branchSuffix,
                warehouseSuffix
            );
        }

        // Find revenue account (for services)
        if (rules.revenue) {
            suggestions.sales = findBestMatchingAccount(
                accounts,
                rules.revenue,
                branchSuffix,
                warehouseSuffix
            );
        }
    }

    return suggestions;
}

// Find best matching account based on patterns and codes
function findBestMatchingAccount(accounts, rule, branchSuffix, warehouseSuffix) {
    let bestMatch = null;
    let bestScore = 0;

    accounts.forEach(account => {
        let score = 0;

        // Check name pattern match
        if (account.accountName.includes(rule.pattern)) {
            score += 50;
        }

        // Check code pattern match
        const accountCodeBase = account.accountCode.substring(0, 4);
        if (rule.codes.includes(accountCodeBase)) {
            score += 40;
        }

        // Check branch suffix match
        if (account.accountCode.includes(branchSuffix)) {
            score += 20;
        }

        // Check warehouse suffix match
        if (account.accountCode.includes(warehouseSuffix)) {
            score += 10;
        }

        // Prefer active accounts
        if (account.isActive) {
            score += 5;
        }

        if (score > bestScore) {
            bestScore = score;
            bestMatch = account;
        }
    });

    return bestMatch;
}

// Load chart of accounts
function loadChartOfAccounts() {
    const savedAccounts = localStorage.getItem('anwar_bakery_accounts');
    if (savedAccounts) {
        return JSON.parse(savedAccounts);
    }

    // Return sample accounts if none exist
    return createSampleChartOfAccounts();
}

// Create sample chart of accounts for testing
function createSampleChartOfAccounts() {
    const sampleAccounts = [
        // Inventory Accounts - Raw Materials
        { id: 1, accountCode: '130101', accountName: 'مخزون الخامات - الرياض الرئيسي', accountType: 'asset', isActive: true },
        { id: 2, accountCode: '130102', accountName: 'مخزون الخامات - جدة', accountType: 'asset', isActive: true },
        { id: 3, accountCode: '130201', accountName: 'مخزون الخامات - مخزن التبريد - الرياض', accountType: 'asset', isActive: true },

        // Inventory Accounts - Finished Products
        { id: 4, accountCode: '131101', accountName: 'مخزون المنتجات النهائية - الرياض الرئيسي', accountType: 'asset', isActive: true },
        { id: 5, accountCode: '131102', accountName: 'مخزون المنتجات النهائية - جدة', accountType: 'asset', isActive: true },

        // Purchase Accounts
        { id: 6, accountCode: '510101', accountName: 'مشتريات الخامات - الرياض الرئيسي', accountType: 'expense', isActive: true },
        { id: 7, accountCode: '510102', accountName: 'مشتريات الخامات - جدة', accountType: 'expense', isActive: true },

        // Sales Accounts
        { id: 8, accountCode: '410101', accountName: 'مبيعات المنتجات - الرياض الرئيسي', accountType: 'revenue', isActive: true },
        { id: 9, accountCode: '410102', accountName: 'مبيعات المنتجات - جدة', accountType: 'revenue', isActive: true },
        { id: 10, accountCode: '420101', accountName: 'إيرادات الخدمات - الرياض الرئيسي', accountType: 'revenue', isActive: true },

        // COGS Accounts
        { id: 11, accountCode: '530101', accountName: 'تكلفة البضاعة المباعة - الرياض الرئيسي', accountType: 'expense', isActive: true },
        { id: 12, accountCode: '530102', accountName: 'تكلفة البضاعة المباعة - جدة', accountType: 'expense', isActive: true },
        { id: 13, accountCode: '520101', accountName: 'تكلفة الخامات المستخدمة - الرياض الرئيسي', accountType: 'expense', isActive: true }
    ];

    // Save sample accounts
    localStorage.setItem('anwar_bakery_accounts', JSON.stringify(sampleAccounts));
    return sampleAccounts;
}

// Populate account dropdowns with suggestions
function populateAccountDropdowns(suggestions) {
    const accounts = loadChartOfAccounts();

    // Populate inventory account
    populateAccountSelect('inventoryAccount', accounts, suggestions.inventory);

    // Populate purchase account
    populateAccountSelect('purchaseAccount', accounts, suggestions.purchase);

    // Populate COGS account
    populateAccountSelect('cogsAccount', accounts, suggestions.cogs);

    // Populate sales account
    populateAccountSelect('salesAccount', accounts, suggestions.sales);

    // Populate custom account selects
    populateAccountSelect('customInventoryAccount', accounts);
    populateAccountSelect('customCogsAccount', accounts);
}

// Populate individual account select
function populateAccountSelect(selectId, accounts, suggestedAccount = null) {
    const select = document.getElementById(selectId);
    if (!select) return;

    select.innerHTML = '<option value="">اختر الحساب</option>';

    accounts.forEach(account => {
        const option = document.createElement('option');
        option.value = account.id;
        option.textContent = `${account.accountCode} - ${account.accountName}`;
        option.dataset.accountCode = account.accountCode;
        option.dataset.accountName = account.accountName;

        // Mark as suggested
        if (suggestedAccount && account.id === suggestedAccount.id) {
            option.selected = true;
            option.textContent += ' (مقترح)';
        }

        select.appendChild(option);
    });
}

// Update account status indicators
function updateAccountStatusIndicators(suggestions) {
    // Update inventory account status
    updateAccountStatus('inventoryAccountStatus', 'inventoryAccountInfo', suggestions.inventory);

    // Update purchase account status
    updateAccountStatus('purchaseAccountStatus', 'purchaseAccountInfo', suggestions.purchase);

    // Update COGS account status
    updateAccountStatus('cogsAccountStatus', 'cogsAccountInfo', suggestions.cogs);

    // Update sales account status
    updateAccountStatus('salesAccountStatus', 'salesAccountInfo', suggestions.sales);
}

// Update individual account status
function updateAccountStatus(statusId, infoId, suggestedAccount) {
    const statusElement = document.getElementById(statusId);
    const infoElement = document.getElementById(infoId);

    if (!statusElement || !infoElement) return;

    if (suggestedAccount) {
        statusElement.textContent = '✅';
        statusElement.title = 'تم العثور على حساب مناسب';
        infoElement.textContent = `مقترح: ${suggestedAccount.accountCode} - ${suggestedAccount.accountName}`;
        infoElement.className = 'text-xs text-green-600 mt-1';
    } else {
        statusElement.textContent = '⚠️';
        statusElement.title = 'لم يتم العثور على حساب مناسب';
        infoElement.textContent = 'لم يتم العثور على حساب مناسب - يرجى الاختيار يدوياً';
        infoElement.className = 'text-xs text-orange-600 mt-1';
    }
}

// Update account mapping rules display
function updateAccountMappingRulesDisplay(itemType, branchId, warehouseId) {
    const rulesContainer = document.getElementById('accountMappingRules');
    if (!rulesContainer) return;

    const rules = accountMappingRules[itemType];
    const branchName = getBranchName(branchId);
    const warehouseName = getWarehouseName(warehouseId);

    if (rules) {
        const rulesList = Object.keys(rules).map(ruleType => {
            const rule = rules[ruleType];
            return `• ${rule.description} (${rule.pattern})`;
        }).join('<br>');

        rulesContainer.innerHTML = `
            <div class="mb-2">
                <strong>نوع الصنف:</strong> ${getItemTypeName(itemType)}<br>
                <strong>الفرع:</strong> ${branchName}<br>
                <strong>المخزن:</strong> ${warehouseName}
            </div>
            <div>
                <strong>القواعد المطبقة:</strong><br>
                ${rulesList}
            </div>
        `;
    } else {
        rulesContainer.innerHTML = 'لا توجد قواعد محددة لهذا النوع من الأصناف';
    }
}

// Get item type name in Arabic
function getItemTypeName(itemType) {
    const typeNames = {
        'raw_material': 'خامة',
        'finished_product': 'منتج نهائي',
        'semi_finished': 'نصف مصنع',
        'service': 'خدمة'
    };
    return typeNames[itemType] || 'غير محدد';
}

// Toggle manual account selection
function toggleManualAccountSelection() {
    const manualSection = document.getElementById('manualAccountSelection');
    if (manualSection.classList.contains('hidden')) {
        manualSection.classList.remove('hidden');
    } else {
        manualSection.classList.add('hidden');
    }
}

// Reset account selections
function resetAccountSelections() {
    const accountSelects = ['inventoryAccount', 'purchaseAccount', 'cogsAccount', 'salesAccount'];
    accountSelects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            select.innerHTML = '<option value="">اختر نوع الصنف أولاً</option>';
        }
    });

    // Reset status indicators
    const statusElements = ['inventoryAccountStatus', 'purchaseAccountStatus', 'cogsAccountStatus', 'salesAccountStatus'];
    statusElements.forEach(statusId => {
        const element = document.getElementById(statusId);
        if (element) {
            element.textContent = '⏳';
        }
    });

    // Reset info elements
    const infoElements = ['inventoryAccountInfo', 'purchaseAccountInfo', 'cogsAccountInfo', 'salesAccountInfo'];
    infoElements.forEach(infoId => {
        const element = document.getElementById(infoId);
        if (element) {
            element.textContent = '';
        }
    });

    // Reset rules display
    const rulesContainer = document.getElementById('accountMappingRules');
    if (rulesContainer) {
        rulesContainer.innerHTML = 'اختر نوع الصنف والفرع والمخزن لعرض القواعد المطبقة';
    }
}

// Event listeners for smart account mapping
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners for automatic account mapping
    const triggerElements = ['itemType', 'branchId', 'warehouseId'];
    triggerElements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            element.addEventListener('change', function() {
                const autoUpdate = document.getElementById('autoUpdateAccounts');
                if (autoUpdate && autoUpdate.checked) {
                    performSmartAccountMapping();
                }
            });
        }
    });
});

// Validate account mapping data
function validateAccountMappingData(itemData) {
    const validation = {
        isValid: true,
        message: '',
        warnings: []
    };

    // Check if required accounts are selected for non-service items
    if (itemData.itemType !== 'service') {
        if (!itemData.accountingIntegration.inventoryAccount) {
            validation.isValid = false;
            validation.message = 'يجب اختيار حساب المخزون للأصناف غير الخدمية';
            return validation;
        }

        if (!itemData.accountingIntegration.cogsAccount) {
            validation.warnings.push('لم يتم اختيار حساب تكلفة البضاعة المباعة');
        }
    }

    // Check if service accounts are selected for services
    if (itemData.itemType === 'service') {
        if (!itemData.accountingIntegration.salesAccount) {
            validation.warnings.push('لم يتم اختيار حساب إيرادات الخدمة');
        }
    }

    // Validate account codes format
    const accounts = loadChartOfAccounts();
    const selectedAccounts = [
        itemData.accountingIntegration.inventoryAccount,
        itemData.accountingIntegration.purchaseAccount,
        itemData.accountingIntegration.cogsAccount,
        itemData.accountingIntegration.salesAccount
    ].filter(accountId => accountId);

    selectedAccounts.forEach(accountId => {
        const account = accounts.find(acc => acc.id == accountId);
        if (!account) {
            validation.isValid = false;
            validation.message = `الحساب المحدد غير موجود في دليل الحسابات`;
            return;
        }

        if (!account.isActive) {
            validation.warnings.push(`الحساب ${account.accountName} غير نشط`);
        }
    });

    return validation;
}

// Create journal entry for opening balance
function createOpeningBalanceJournalEntry(itemData) {
    if (!itemData.accountingIntegration.autoCreateJournalEntry) {
        return null;
    }

    if (itemData.totalOpeningValue <= 0) {
        return null;
    }

    const journalEntry = {
        id: Date.now(),
        entryNumber: `OB-${itemData.itemCode}-${new Date().getFullYear()}`,
        date: itemData.openingDate || new Date().toISOString().split('T')[0],
        description: `رصيد افتتاحي للصنف: ${itemData.itemName}`,
        reference: `كود الصنف: ${itemData.itemCode}`,
        type: 'opening_balance',
        status: 'posted',
        createdBy: 'system',
        createdAt: new Date().toISOString(),
        entries: []
    };

    // Debit inventory account
    if (itemData.accountingIntegration.inventoryAccount) {
        const inventoryAccount = loadChartOfAccounts().find(acc =>
            acc.id == itemData.accountingIntegration.inventoryAccount
        );

        if (inventoryAccount) {
            journalEntry.entries.push({
                accountId: inventoryAccount.id,
                accountCode: inventoryAccount.accountCode,
                accountName: inventoryAccount.accountName,
                debit: itemData.totalOpeningValue,
                credit: 0,
                description: `رصيد افتتاحي - ${itemData.itemName}`
            });
        }
    }

    // Credit opening balance equity account
    const openingBalanceAccount = loadChartOfAccounts().find(acc =>
        acc.accountName.includes('الأرصدة الافتتاحية') ||
        acc.accountCode.startsWith('3001')
    );

    if (openingBalanceAccount) {
        journalEntry.entries.push({
            accountId: openingBalanceAccount.id,
            accountCode: openingBalanceAccount.accountCode,
            accountName: openingBalanceAccount.accountName,
            debit: 0,
            credit: itemData.totalOpeningValue,
            description: `رصيد افتتاحي - ${itemData.itemName}`
        });
    } else {
        // Create default opening balance account if not exists
        const defaultOpeningAccount = {
            id: Date.now() + 1,
            accountCode: '300101',
            accountName: 'الأرصدة الافتتاحية - المخزون',
            accountType: 'equity',
            isActive: true,
            parentId: null,
            level: 1
        };

        // Add to chart of accounts
        const accounts = loadChartOfAccounts();
        accounts.push(defaultOpeningAccount);
        localStorage.setItem('anwar_bakery_accounts', JSON.stringify(accounts));

        journalEntry.entries.push({
            accountId: defaultOpeningAccount.id,
            accountCode: defaultOpeningAccount.accountCode,
            accountName: defaultOpeningAccount.accountName,
            debit: 0,
            credit: itemData.totalOpeningValue,
            description: `رصيد افتتاحي - ${itemData.itemName}`
        });
    }

    // Save journal entry
    const savedEntries = localStorage.getItem('anwar_bakery_journal_entries');
    let journalEntries = savedEntries ? JSON.parse(savedEntries) : [];
    journalEntries.push(journalEntry);
    localStorage.setItem('anwar_bakery_journal_entries', JSON.stringify(journalEntries));

    return journalEntry;
}

// Show accounting integration summary
function showAccountingIntegrationSummary(itemData) {
    if (!itemData.accountingIntegration) return;

    const accounts = loadChartOfAccounts();
    let summary = `📊 ملخص الربط المحاسبي للصنف: ${itemData.itemName}\n\n`;

    // Inventory account
    if (itemData.accountingIntegration.inventoryAccount) {
        const account = accounts.find(acc => acc.id == itemData.accountingIntegration.inventoryAccount);
        if (account) {
            summary += `🏪 حساب المخزون: ${account.accountCode} - ${account.accountName}\n`;
        }
    }

    // Purchase account
    if (itemData.accountingIntegration.purchaseAccount) {
        const account = accounts.find(acc => acc.id == itemData.accountingIntegration.purchaseAccount);
        if (account) {
            summary += `🛒 حساب المشتريات: ${account.accountCode} - ${account.accountName}\n`;
        }
    }

    // COGS account
    if (itemData.accountingIntegration.cogsAccount) {
        const account = accounts.find(acc => acc.id == itemData.accountingIntegration.cogsAccount);
        if (account) {
            summary += `💰 حساب تكلفة البضاعة: ${account.accountCode} - ${account.accountName}\n`;
        }
    }

    // Sales account
    if (itemData.accountingIntegration.salesAccount) {
        const account = accounts.find(acc => acc.id == itemData.accountingIntegration.salesAccount);
        if (account) {
            summary += `💵 حساب المبيعات: ${account.accountCode} - ${account.accountName}\n`;
        }
    }

    summary += `\n⚙️ الإعدادات:\n`;
    summary += `• إنشاء قيد تلقائي: ${itemData.accountingIntegration.autoCreateJournalEntry ? 'نعم' : 'لا'}\n`;
    summary += `• تحديث تلقائي: ${itemData.accountingIntegration.autoUpdateAccounts ? 'نعم' : 'لا'}\n`;
    summary += `• التحقق من الربط: ${itemData.accountingIntegration.validateAccountMapping ? 'نعم' : 'لا'}\n`;

    if (itemData.totalOpeningValue > 0) {
        summary += `\n💰 الرصيد الافتتاحي: ${itemData.totalOpeningValue.toFixed(2)} ريال\n`;
        if (itemData.accountingIntegration.autoCreateJournalEntry) {
            summary += `✅ سيتم إنشاء قيد محاسبي للرصيد الافتتاحي تلقائياً`;
        }
    }

    return summary;
}

// Damage Entry Functions

let currentDamageItem = null;

// Show damage modal for specific item
function showDamageModal(itemId) {
    const item = items.find(i => i.id === itemId);
    if (!item) {
        showMessage('الصنف غير موجود', 'error');
        return;
    }

    if (item.itemType === 'service') {
        showMessage('لا يمكن إتلاف الخدمات', 'error');
        return;
    }

    currentDamageItem = item;

    // Show modal
    document.getElementById('damageModal').style.display = 'block';

    // Populate item information
    populateDamageItemInfo(item);

    // Populate warehouses and units
    populateDamageWarehouses(item);
    populateDamageUnits(item);

    // Set default date to today
    document.getElementById('damageDate').value = new Date().toISOString().split('T')[0];

    // Setup smart accounting
    setupDamageAccounting(item);

    // Clear form
    clearDamageForm();
}

// Close damage modal
function closeDamageModal() {
    document.getElementById('damageModal').style.display = 'none';
    currentDamageItem = null;
    clearDamageForm();
}

// Populate damage item information
function populateDamageItemInfo(item) {
    const infoDiv = document.getElementById('damageItemInfo');
    const currencySymbol = getCurrencySymbol();

    infoDiv.innerHTML = `
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700">كود الصنف</label>
                <div class="text-lg font-semibold text-blue-600">${item.itemCode}</div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">اسم الصنف</label>
                <div class="text-lg font-semibold text-gray-900">${item.itemName}</div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">المخزون الحالي</label>
                <div class="text-lg font-semibold ${item.currentStock <= item.minStock ? 'text-red-600' : 'text-green-600'}">
                    ${item.currentStock} ${item.unitsData?.smallUnitName || 'وحدة'}
                </div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">متوسط التكلفة</label>
                <div class="text-lg font-semibold text-purple-600">
                    ${item.averageOpeningCost || item.costPrice || 0} ${currencySymbol}
                </div>
            </div>
        </div>
    `;
}

// Populate damage warehouses
function populateDamageWarehouses(item) {
    const warehouseSelect = document.getElementById('damageWarehouse');
    warehouseSelect.innerHTML = '<option value="">اختر المخزن</option>';

    // Get warehouses for the item's branch
    const savedWarehouses = localStorage.getItem('anwar_bakery_warehouses');
    if (savedWarehouses) {
        const warehouses = JSON.parse(savedWarehouses);
        const itemWarehouses = warehouses.filter(warehouse =>
            warehouse.branchId == item.branchId && warehouse.isActive
        );

        itemWarehouses.forEach(warehouse => {
            const option = document.createElement('option');
            option.value = warehouse.id;
            option.textContent = warehouse.warehouseName;
            option.dataset.warehouseName = warehouse.warehouseName;
            warehouseSelect.appendChild(option);
        });
    }
}

// Populate damage units
function populateDamageUnits(item) {
    const unitSelect = document.getElementById('damageUnit');
    unitSelect.innerHTML = '<option value="">اختر الوحدة</option>';

    // Add small unit
    if (item.unitsData?.smallUnit) {
        const smallOption = document.createElement('option');
        smallOption.value = 'small';
        smallOption.textContent = item.unitsData.smallUnitName || 'الوحدة الصغرى';
        smallOption.dataset.unitName = item.unitsData.smallUnitName || 'الوحدة الصغرى';
        smallOption.dataset.unitPrice = item.unitsData.smallUnitPrice || item.costPrice || 0;
        smallOption.dataset.conversionFactor = 1;
        unitSelect.appendChild(smallOption);
    }

    // Add large unit if exists
    if (item.unitsData?.largeUnit && item.unitsData?.conversionFactor > 1) {
        const largeOption = document.createElement('option');
        largeOption.value = 'large';
        largeOption.textContent = item.unitsData.largeUnitName || 'الوحدة الكبرى';
        largeOption.dataset.unitName = item.unitsData.largeUnitName || 'الوحدة الكبرى';
        largeOption.dataset.unitPrice = item.unitsData.largeUnitPrice || (item.costPrice * item.unitsData.conversionFactor) || 0;
        largeOption.dataset.conversionFactor = item.unitsData.conversionFactor || 1;
        unitSelect.appendChild(largeOption);
    }
}

// Update available stock when warehouse changes
function updateAvailableStock() {
    const warehouseSelect = document.getElementById('damageWarehouse');
    const stockInfo = document.getElementById('warehouseStockInfo');

    if (!warehouseSelect.value || !currentDamageItem) {
        stockInfo.textContent = '';
        return;
    }

    // In a real system, this would fetch actual warehouse stock
    // For now, we'll use the total stock
    const availableStock = currentDamageItem.currentStock || 0;
    const warehouseName = warehouseSelect.options[warehouseSelect.selectedIndex].dataset.warehouseName;

    if (availableStock > 0) {
        stockInfo.innerHTML = `<span class="text-green-600">متاح في ${warehouseName}: ${availableStock} وحدة</span>`;
    } else {
        stockInfo.innerHTML = `<span class="text-red-600">لا يوجد مخزون متاح في ${warehouseName}</span>`;
    }
}

// Update unit price when unit changes
function updateUnitPrice() {
    const unitSelect = document.getElementById('damageUnit');
    const priceInfo = document.getElementById('unitPriceInfo');
    const unitCostInput = document.getElementById('damageUnitCost');

    if (!unitSelect.value) {
        priceInfo.textContent = '';
        unitCostInput.value = '';
        return;
    }

    const selectedOption = unitSelect.options[unitSelect.selectedIndex];
    const unitPrice = parseFloat(selectedOption.dataset.unitPrice) || 0;
    const unitName = selectedOption.dataset.unitName;
    const currencySymbol = getCurrencySymbol();

    unitCostInput.value = unitPrice.toFixed(2);
    priceInfo.innerHTML = `<span class="text-blue-600">متوسط تكلفة ${unitName}: ${unitPrice.toFixed(2)} ${currencySymbol}</span>`;

    calculateDamageValue();
}

// Calculate damage value
function calculateDamageValue() {
    const quantity = parseFloat(document.getElementById('damageQuantity').value) || 0;
    const unitCost = parseFloat(document.getElementById('damageUnitCost').value) || 0;
    const totalValue = quantity * unitCost;

    document.getElementById('totalDamageValue').value = totalValue.toFixed(2);

    // Validate quantity against available stock
    validateDamageQuantity();
}

// Validate damage quantity
function validateDamageQuantity() {
    const quantityInput = document.getElementById('damageQuantity');
    const validation = document.getElementById('quantityValidation');
    const unitSelect = document.getElementById('damageUnit');

    const quantity = parseFloat(quantityInput.value) || 0;
    const availableStock = currentDamageItem?.currentStock || 0;

    if (!unitSelect.value) {
        validation.innerHTML = '<span class="text-gray-500">اختر الوحدة أولاً</span>';
        return false;
    }

    // Convert quantity to small units for comparison
    const selectedOption = unitSelect.options[unitSelect.selectedIndex];
    const conversionFactor = parseFloat(selectedOption.dataset.conversionFactor) || 1;
    const quantityInSmallUnits = quantity * conversionFactor;

    if (quantityInSmallUnits > availableStock) {
        validation.innerHTML = `<span class="text-red-600">الكمية تتجاوز المخزون المتاح (${availableStock} وحدة صغرى)</span>`;
        return false;
    } else if (quantity <= 0) {
        validation.innerHTML = '<span class="text-red-600">يجب أن تكون الكمية أكبر من صفر</span>';
        return false;
    } else {
        validation.innerHTML = `<span class="text-green-600">✓ الكمية صحيحة (${quantityInSmallUnits} وحدة صغرى)</span>`;
        return true;
    }
}

// Setup damage accounting
function setupDamageAccounting(item) {
    const accounts = loadChartOfAccounts();

    // Find damage expense account
    const damageAccount = findDamageExpenseAccount(accounts, item);
    populateAccountSelect('damageExpenseAccount', accounts, damageAccount);

    // Find inventory account
    const inventoryAccount = findInventoryAccountForItem(accounts, item);
    populateAccountSelect('damageInventoryAccount', accounts, inventoryAccount);

    // Update account info
    updateDamageAccountInfo(damageAccount, inventoryAccount);
}

// Find damage expense account
function findDamageExpenseAccount(accounts, item) {
    // Look for damage/loss accounts
    const damagePatterns = ['إتلاف', 'تلف', 'خسائر المخزون', 'فقدان'];

    let bestMatch = null;
    let bestScore = 0;

    accounts.forEach(account => {
        let score = 0;

        // Check for damage patterns in name
        damagePatterns.forEach(pattern => {
            if (account.accountName.includes(pattern)) {
                score += 30;
            }
        });

        // Check for expense account type
        if (account.accountType === 'expense') {
            score += 20;
        }

        // Check for account code patterns (usually 5xxx for expenses)
        if (account.accountCode.startsWith('5')) {
            score += 10;
        }

        // Prefer active accounts
        if (account.isActive) {
            score += 5;
        }

        if (score > bestScore) {
            bestScore = score;
            bestMatch = account;
        }
    });

    return bestMatch;
}

// Find inventory account for item
function findInventoryAccountForItem(accounts, item) {
    if (item.accountingIntegration?.inventoryAccount) {
        return accounts.find(acc => acc.id == item.accountingIntegration.inventoryAccount);
    }

    // Use smart mapping to find inventory account
    const suggestions = getSuggestedAccounts(item.itemType, item.branchId, item.warehouseId);
    return suggestions.inventory;
}

// Update damage account info
function updateDamageAccountInfo(damageAccount, inventoryAccount) {
    const damageInfo = document.getElementById('damageAccountInfo');
    const inventoryInfo = document.getElementById('inventoryAccountInfo');

    if (damageAccount) {
        damageInfo.innerHTML = `<span class="text-green-600">✓ ${damageAccount.accountCode} - ${damageAccount.accountName}</span>`;
    } else {
        damageInfo.innerHTML = '<span class="text-orange-600">⚠️ لم يتم العثور على حساب إتلاف مناسب</span>';
    }

    if (inventoryAccount) {
        inventoryInfo.innerHTML = `<span class="text-green-600">✓ ${inventoryAccount.accountCode} - ${inventoryAccount.accountName}</span>`;
    } else {
        inventoryInfo.innerHTML = '<span class="text-orange-600">⚠️ لم يتم العثور على حساب مخزون مناسب</span>';
    }
}

// Clear damage form
function clearDamageForm() {
    document.getElementById('damageForm').reset();
    document.getElementById('quantityValidation').innerHTML = '';
    document.getElementById('warehouseStockInfo').innerHTML = '';
    document.getElementById('unitPriceInfo').innerHTML = '';
    document.getElementById('totalDamageValue').value = '';
}

// Process damage entry
function processDamageEntry(event) {
    event.preventDefault();

    if (!currentDamageItem) {
        showMessage('خطأ: لم يتم تحديد الصنف', 'error');
        return;
    }

    // Validate form
    if (!validateDamageForm()) {
        return;
    }

    // Create damage entry
    const damageEntry = createDamageEntry();

    // Update item stock
    updateItemStockAfterDamage(damageEntry);

    // Create journal entry if enabled
    if (document.getElementById('autoCreateDamageEntry').checked) {
        createDamageJournalEntry(damageEntry);
    }

    // Save damage entry
    saveDamageEntry(damageEntry);

    // Close modal and refresh
    closeDamageModal();
    renderItems();
    updateStats();

    showMessage(`تم تسجيل إتلاف ${damageEntry.quantity} ${damageEntry.unitName} من ${currentDamageItem.itemName} بنجاح`, 'success');
}

// Validate damage form
function validateDamageForm() {
    const warehouse = document.getElementById('damageWarehouse').value;
    const unit = document.getElementById('damageUnit').value;
    const quantity = parseFloat(document.getElementById('damageQuantity').value);
    const reason = document.getElementById('damageReason').value;
    const date = document.getElementById('damageDate').value;

    if (!warehouse) {
        showMessage('يرجى اختيار المخزن', 'error');
        return false;
    }

    if (!unit) {
        showMessage('يرجى اختيار الوحدة', 'error');
        return false;
    }

    if (!quantity || quantity <= 0) {
        showMessage('يرجى إدخال كمية صحيحة', 'error');
        return false;
    }

    if (!validateDamageQuantity()) {
        showMessage('الكمية المدخلة غير صحيحة', 'error');
        return false;
    }

    if (!reason) {
        showMessage('يرجى اختيار سبب الإتلاف', 'error');
        return false;
    }

    if (!date) {
        showMessage('يرجى تحديد تاريخ الإتلاف', 'error');
        return false;
    }

    return true;
}

// Create damage entry object
function createDamageEntry() {
    const unitSelect = document.getElementById('damageUnit');
    const selectedOption = unitSelect.options[unitSelect.selectedIndex];
    const warehouseSelect = document.getElementById('damageWarehouse');
    const warehouseOption = warehouseSelect.options[warehouseSelect.selectedIndex];

    return {
        id: Date.now(),
        entryNumber: `DMG-${currentDamageItem.itemCode}-${Date.now()}`,
        itemId: currentDamageItem.id,
        itemCode: currentDamageItem.itemCode,
        itemName: currentDamageItem.itemName,
        warehouseId: parseInt(warehouseSelect.value),
        warehouseName: warehouseOption.dataset.warehouseName,
        unitType: unitSelect.value,
        unitName: selectedOption.dataset.unitName,
        conversionFactor: parseFloat(selectedOption.dataset.conversionFactor) || 1,
        quantity: parseFloat(document.getElementById('damageQuantity').value),
        unitCost: parseFloat(document.getElementById('damageUnitCost').value),
        totalValue: parseFloat(document.getElementById('totalDamageValue').value),
        reason: document.getElementById('damageReason').value,
        notes: document.getElementById('damageNotes').value,
        date: document.getElementById('damageDate').value,
        approvedBy: document.getElementById('approvedBy').value,
        approvalNumber: document.getElementById('approvalNumber').value,
        damageExpenseAccount: document.getElementById('damageExpenseAccount').value,
        damageInventoryAccount: document.getElementById('damageInventoryAccount').value,
        createdAt: new Date().toISOString(),
        createdBy: 'current_user' // TODO: Get from session
    };
}

// Update item stock after damage
function updateItemStockAfterDamage(damageEntry) {
    const item = items.find(i => i.id === currentDamageItem.id);
    if (item) {
        // Convert to small units for stock deduction
        const quantityInSmallUnits = damageEntry.quantity * damageEntry.conversionFactor;
        item.currentStock = Math.max(0, item.currentStock - quantityInSmallUnits);

        // Update in localStorage
        saveItems();
    }
}

// Create damage journal entry
function createDamageJournalEntry(damageEntry) {
    const accounts = loadChartOfAccounts();
    const damageAccount = accounts.find(acc => acc.id == damageEntry.damageExpenseAccount);
    const inventoryAccount = accounts.find(acc => acc.id == damageEntry.damageInventoryAccount);

    if (!damageAccount || !inventoryAccount) {
        showMessage('تحذير: لم يتم إنشاء قيد محاسبي بسبب عدم وجود الحسابات المطلوبة', 'warning');
        return;
    }

    const journalEntry = {
        id: Date.now() + 1,
        entryNumber: `JE-${damageEntry.entryNumber}`,
        date: damageEntry.date,
        description: `قيد إتلاف - ${damageEntry.itemName}`,
        reference: `رقم الإتلاف: ${damageEntry.entryNumber}`,
        type: 'damage_entry',
        status: 'posted',
        createdBy: damageEntry.createdBy,
        createdAt: new Date().toISOString(),
        entries: [
            {
                accountId: damageAccount.id,
                accountCode: damageAccount.accountCode,
                accountName: damageAccount.accountName,
                debit: damageEntry.totalValue,
                credit: 0,
                description: `إتلاف ${damageEntry.quantity} ${damageEntry.unitName} - ${damageEntry.reason}`
            },
            {
                accountId: inventoryAccount.id,
                accountCode: inventoryAccount.accountCode,
                accountName: inventoryAccount.accountName,
                debit: 0,
                credit: damageEntry.totalValue,
                description: `إتلاف ${damageEntry.quantity} ${damageEntry.unitName} - ${damageEntry.reason}`
            }
        ]
    };

    // Save journal entry
    const savedEntries = localStorage.getItem('anwar_bakery_journal_entries');
    let journalEntries = savedEntries ? JSON.parse(savedEntries) : [];
    journalEntries.push(journalEntry);
    localStorage.setItem('anwar_bakery_journal_entries', JSON.stringify(journalEntries));

    damageEntry.journalEntryId = journalEntry.id;
}

// Save damage entry
function saveDamageEntry(damageEntry) {
    const savedDamageEntries = localStorage.getItem('anwar_bakery_damage_entries');
    let damageEntries = savedDamageEntries ? JSON.parse(savedDamageEntries) : [];
    damageEntries.push(damageEntry);
    localStorage.setItem('anwar_bakery_damage_entries', JSON.stringify(damageEntries));
}

// New Damage Entry Functions
function openDamageEntryModal() {
    document.getElementById('damageEntryModal').classList.add('active');
    document.getElementById('damageEntryModal').style.display = 'flex';
    generateDamageEntryNumber();
    document.getElementById('damageDate').value = new Date().toISOString().split('T')[0];
    populateDamageItemsDropdown();
    populateDamageWarehousesDropdown();
}

function closeDamageEntryModal() {
    document.getElementById('damageEntryModal').classList.remove('active');
    document.getElementById('damageEntryModal').style.display = 'none';
    document.getElementById('damageEntryForm').reset();
}

function generateDamageEntryNumber() {
    const savedEntries = localStorage.getItem('anwar_bakery_damage_entries');
    let entries = savedEntries ? JSON.parse(savedEntries) : [];
    const nextId = Math.max(...entries.map(e => e.id), 0) + 1;
    const entryNumber = `DMG-${nextId.toString().padStart(6, '0')}`;
    document.getElementById('damageEntryNumber').value = entryNumber;
}

function populateDamageItemsDropdown() {
    const dropdown = document.getElementById('damageItemId');
    dropdown.innerHTML = '<option value="">اختر الصنف</option>';

    items.forEach(item => {
        if (item.isActive) {
            const option = document.createElement('option');
            option.value = item.id;
            option.textContent = `${item.name} (${item.code})`;
            option.dataset.unitName = item.smallUnitName;
            option.dataset.unitCost = item.costPrice || 0;
            dropdown.appendChild(option);
        }
    });
}

function populateDamageWarehousesDropdown() {
    const dropdown = document.getElementById('damageWarehouseId');
    dropdown.innerHTML = '<option value="">اختر المخزن</option>';

    warehouses.forEach(warehouse => {
        if (warehouse.isActive) {
            const option = document.createElement('option');
            option.value = warehouse.id;
            option.textContent = warehouse.name;
            dropdown.appendChild(option);
        }
    });
}

function updateDamageItemDetails() {
    const dropdown = document.getElementById('damageItemId');
    const selectedOption = dropdown.options[dropdown.selectedIndex];

    if (selectedOption.value) {
        document.getElementById('damageUnitName').value = selectedOption.dataset.unitName || '';
        document.getElementById('damageUnitCost').value = selectedOption.dataset.unitCost || 0;
        calculateDamageTotal();
    } else {
        document.getElementById('damageUnitName').value = '';
        document.getElementById('damageUnitCost').value = '';
        document.getElementById('damageTotalValue').value = '';
    }
}

function calculateDamageTotal() {
    const quantity = parseFloat(document.getElementById('damageQuantity').value) || 0;
    const unitCost = parseFloat(document.getElementById('damageUnitCost').value) || 0;
    const total = quantity * unitCost;
    document.getElementById('damageTotalValue').value = total.toFixed(2);
}

function saveDamageEntry(event) {
    event.preventDefault();

    const itemDropdown = document.getElementById('damageItemId');
    const warehouseDropdown = document.getElementById('damageWarehouseId');
    const selectedItem = itemDropdown.options[itemDropdown.selectedIndex];
    const selectedWarehouse = warehouseDropdown.options[warehouseDropdown.selectedIndex];

    const damageEntry = {
        id: Date.now(),
        entryNumber: document.getElementById('damageEntryNumber').value,
        date: document.getElementById('damageDate').value,
        itemId: parseInt(itemDropdown.value),
        itemCode: selectedItem.textContent.match(/\(([^)]+)\)/)[1],
        itemName: selectedItem.textContent.split(' (')[0],
        warehouseId: parseInt(warehouseDropdown.value),
        warehouseName: selectedWarehouse.textContent,
        unitType: 'small',
        unitName: document.getElementById('damageUnitName').value,
        conversionFactor: 1,
        quantity: parseFloat(document.getElementById('damageQuantity').value),
        unitCost: parseFloat(document.getElementById('damageUnitCost').value),
        totalValue: parseFloat(document.getElementById('damageTotalValue').value),
        reason: document.getElementById('damageReason').value,
        notes: document.getElementById('damageNotes').value,
        createdBy: 'مدير النظام',
        createdAt: new Date().toISOString()
    };

    // Save to localStorage
    const savedEntries = localStorage.getItem('anwar_bakery_damage_entries');
    let entries = savedEntries ? JSON.parse(savedEntries) : [];
    entries.push(damageEntry);
    localStorage.setItem('anwar_bakery_damage_entries', JSON.stringify(entries));

    closeDamageEntryModal();
    showMessage('تم حفظ قيد التالف بنجاح!', 'success');

    // Optionally redirect to damage entry page
    setTimeout(() => {
        if (confirm('هل تريد الانتقال إلى صفحة قيود التالف لعرض جميع القيود؟')) {
            window.location.href = 'damage-entry.html';
        }
    }, 1000);
}

// Advanced Export Products to Excel
function exportProductsToExcel() {
    showMessage('🔄 جاري تصدير المنتجات إلى Excel...', 'info');

    if (typeof window.advancedExcel !== 'undefined') {
        try {
            // Prepare products data for advanced export
            const productsData = items.map(item => ({
                'كود الصنف': item.itemCode,
                'اسم الصنف': item.itemName,
                'النوع': getItemTypeText(item.itemType),
                'الفئة': getCategoryText(item.category),
                'سعر التكلفة': item.costPrice,
                'سعر البيع': item.sellingPrice,
                'الوحدة': item.unit,
                'المخزون الحالي': item.currentStock,
                'الحد الأدنى': item.minStock,
                'الحد الأقصى': item.maxStock,
                'الفرع': item.branchName,
                'المخزن': item.warehouseName,
                'الحالة': item.isActive ? 'نشط' : 'غير نشط'
            }));

            const reportData = {
                main: productsData,
                summary: {
                    totalProducts: items.length,
                    activeProducts: items.filter(item => item.isActive).length,
                    totalValue: items.reduce((sum, item) => sum + (item.currentStock * item.costPrice), 0),
                    lowStockItems: items.filter(item => item.currentStock <= item.minStock).length
                }
            };

            const result = window.advancedExcel.exportInventoryReport(reportData, {
                includeValuation: true,
                includeMovements: false
            });

            if (result.success) {
                showMessage('✅ تم تصدير المنتجات بنجاح!', 'success');
            } else {
                showMessage('❌ فشل في تصدير المنتجات: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('Export error:', error);
            showMessage('❌ خطأ في تصدير المنتجات: ' + error.message, 'error');
        }
    } else {
        // Fallback to basic export
        const basicData = items.map(item => ({
            'كود الصنف': item.itemCode,
            'اسم الصنف': item.itemName,
            'سعر البيع': item.sellingPrice,
            'المخزون الحالي': item.currentStock,
            'الحالة': item.isActive ? 'نشط' : 'غير نشط'
        }));

        if (typeof window.excelUtils !== 'undefined') {
            window.excelUtils.exportToExcel(basicData, 'المنتجات', 'قائمة المنتجات');
            showMessage('✅ تم تصدير المنتجات بنجاح!', 'success');
        } else {
            showMessage('❌ نظام التصدير غير متوفر', 'error');
        }
    }
}

// Print Products List
function printProductsList() {
    showMessage('🔄 جاري تحضير قائمة المنتجات للطباعة...', 'info');

    if (typeof window.advancedPrint !== 'undefined') {
        try {
            const productsData = {
                number: 'PRD-LIST-' + Date.now(),
                date: new Date().toISOString().split('T')[0],
                time: new Date().toISOString(),
                type: 'products_list',
                items: items.filter(item => item.isActive).map(item => ({
                    name: item.itemName,
                    quantity: item.currentStock,
                    unit: item.unit,
                    price: item.sellingPrice,
                    total: item.currentStock * item.sellingPrice
                })),
                subtotal: items.reduce((sum, item) => sum + (item.currentStock * item.sellingPrice), 0),
                tax: 0,
                total: items.reduce((sum, item) => sum + (item.currentStock * item.sellingPrice), 0),
                notes: 'قائمة المنتجات النشطة'
            };

            window.advancedPrint.printInvoice(productsData, {
                showLogo: true,
                showHeader: true,
                showFooter: true,
                copies: 1,
                paperSize: 'A4'
            });

            showMessage('✅ تم إرسال قائمة المنتجات للطباعة!', 'success');
        } catch (error) {
            console.error('Print error:', error);
            showMessage('❌ خطأ في طباعة قائمة المنتجات: ' + error.message, 'error');
        }
    } else {
        // Fallback to basic print
        window.print();
        showMessage('✅ تم إرسال الصفحة للطباعة!', 'success');
    }
}

// Helper functions for text conversion
function getItemTypeText(type) {
    const types = {
        'raw_material': 'مادة خام',
        'finished_product': 'منتج نهائي',
        'service': 'خدمة'
    };
    return types[type] || type;
}

function getCategoryText(category) {
    const categories = {
        'flour': 'دقيق',
        'bread': 'خبز',
        'sugar': 'سكر',
        'decoration': 'تزيين',
        'delivery': 'توصيل'
    };
    return categories[category] || category;
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    checkAuth();
    loadUserInfo();
    loadCompanyInfo();
    loadItems();
});
