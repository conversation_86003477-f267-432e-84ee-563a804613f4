-- =====================================================
-- قاعدة بيانات نظام إدارة مخبز أنوار الحي
-- Database Schema for Anwar Bakery Management System
-- =====================================================

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS anwar_bakery
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

USE anwar_bakery;

-- =====================================================
-- جداول النظام الأساسية (System Tables)
-- =====================================================

-- جدول إعدادات الشركة
CREATE TABLE company_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_name_ar VARCHAR(255) NOT NULL,
    company_name_en VARCHAR(255),
    company_slogan VARCHAR(500),
    business_type ENUM('bakery', 'pastry', 'cafe', 'restaurant', 'other') DEFAULT 'bakery',
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(255),
    address TEXT NOT NULL,
    city VARCHAR(100) DEFAULT 'الرياض',
    country VARCHAR(100) DEFAULT 'السعودية',
    commercial_register VARCHAR(50),
    tax_number VARCHAR(50),
    working_hours VARCHAR(100),
    working_days VARCHAR(100),
    logo_url VARCHAR(500),
    currency_code VARCHAR(3) DEFAULT 'SAR',
    fiscal_year_start DATE DEFAULT '2024-01-01',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول المستخدمين
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    role ENUM('admin', 'manager', 'cashier', 'viewer') NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_active (is_active)
);

-- جدول الفروع والمخازن
CREATE TABLE branches (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    type ENUM('branch', 'warehouse', 'both') DEFAULT 'branch',
    address TEXT,
    phone VARCHAR(20),
    manager_name VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    opening_balance DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_type (type),
    INDEX idx_active (is_active)
);

-- جدول وحدات القياس
CREATE TABLE units (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    symbol VARCHAR(10) NOT NULL,
    type ENUM('weight', 'volume', 'length', 'piece', 'other') DEFAULT 'piece',
    base_unit_id INT NULL,
    conversion_factor DECIMAL(10,4) DEFAULT 1.0000,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (base_unit_id) REFERENCES units(id) ON DELETE SET NULL,
    INDEX idx_type (type),
    INDEX idx_active (is_active)
);

-- =====================================================
-- جداول المحاسبة (Accounting Tables)
-- =====================================================

-- جدول شجرة الحسابات
CREATE TABLE chart_of_accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_code VARCHAR(20) UNIQUE NOT NULL,
    account_name VARCHAR(255) NOT NULL,
    account_type ENUM('assets', 'liabilities', 'equity', 'revenue', 'expense') NOT NULL,
    parent_id INT NULL,
    level INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    opening_balance DECIMAL(15,2) DEFAULT 0.00,
    current_balance DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (parent_id) REFERENCES chart_of_accounts(id) ON DELETE SET NULL,
    INDEX idx_code (account_code),
    INDEX idx_type (account_type),
    INDEX idx_parent (parent_id),
    INDEX idx_active (is_active)
);

-- جدول القيود اليومية
CREATE TABLE journal_entries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    entry_number VARCHAR(50) UNIQUE NOT NULL,
    entry_date DATE NOT NULL,
    description TEXT NOT NULL,
    reference_type ENUM('manual', 'invoice', 'voucher', 'adjustment', 'closing') DEFAULT 'manual',
    reference_id INT NULL,
    total_debit DECIMAL(15,2) NOT NULL,
    total_credit DECIMAL(15,2) NOT NULL,
    status ENUM('draft', 'posted', 'cancelled') DEFAULT 'draft',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_entry_number (entry_number),
    INDEX idx_date (entry_date),
    INDEX idx_reference (reference_type, reference_id),
    INDEX idx_status (status)
);

-- جدول تفاصيل القيود اليومية
CREATE TABLE journal_entry_details (
    id INT PRIMARY KEY AUTO_INCREMENT,
    journal_entry_id INT NOT NULL,
    account_id INT NOT NULL,
    description VARCHAR(500),
    debit_amount DECIMAL(15,2) DEFAULT 0.00,
    credit_amount DECIMAL(15,2) DEFAULT 0.00,

    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id),
    INDEX idx_journal_entry (journal_entry_id),
    INDEX idx_account (account_id)
);

-- =====================================================
-- جداول المنتجات والمخزون (Products & Inventory)
-- =====================================================

-- جدول فئات المنتجات
CREATE TABLE product_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_id INT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (parent_id) REFERENCES product_categories(id) ON DELETE SET NULL,
    INDEX idx_parent (parent_id),
    INDEX idx_active (is_active)
);

-- جدول المنتجات
CREATE TABLE products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category_id INT,
    unit_id INT NOT NULL,
    cost_price DECIMAL(10,2) DEFAULT 0.00,
    selling_price DECIMAL(10,2) NOT NULL,
    min_stock_level DECIMAL(10,2) DEFAULT 0.00,
    max_stock_level DECIMAL(10,2) DEFAULT 0.00,
    current_stock DECIMAL(10,2) DEFAULT 0.00,
    reorder_point DECIMAL(10,2) DEFAULT 0.00,
    barcode VARCHAR(100),
    image_url VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    track_inventory BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (category_id) REFERENCES product_categories(id) ON DELETE SET NULL,
    FOREIGN KEY (unit_id) REFERENCES units(id),
    INDEX idx_code (code),
    INDEX idx_category (category_id),
    INDEX idx_barcode (barcode),
    INDEX idx_active (is_active)
);

-- جدول حركة المخزون
CREATE TABLE inventory_movements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_id INT NOT NULL,
    branch_id INT NOT NULL,
    movement_type ENUM('in', 'out', 'adjustment', 'transfer') NOT NULL,
    quantity DECIMAL(10,2) NOT NULL,
    unit_cost DECIMAL(10,2) DEFAULT 0.00,
    total_cost DECIMAL(15,2) DEFAULT 0.00,
    reference_type ENUM('purchase', 'sale', 'return', 'adjustment', 'transfer', 'damage') NOT NULL,
    reference_id INT NULL,
    notes TEXT,
    movement_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT NOT NULL,

    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (branch_id) REFERENCES branches(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_product (product_id),
    INDEX idx_branch (branch_id),
    INDEX idx_date (movement_date),
    INDEX idx_reference (reference_type, reference_id)
);

-- =====================================================
-- جداول العملاء والموردين (Customers & Suppliers)
-- =====================================================

-- جدول العملاء
CREATE TABLE customers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    city VARCHAR(100),
    tax_number VARCHAR(50),
    credit_limit DECIMAL(15,2) DEFAULT 0.00,
    current_balance DECIMAL(15,2) DEFAULT 0.00,
    customer_type ENUM('individual', 'company') DEFAULT 'individual',
    is_active BOOLEAN DEFAULT TRUE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_code (code),
    INDEX idx_name (name),
    INDEX idx_phone (phone),
    INDEX idx_active (is_active)
);

-- جدول الموردين
CREATE TABLE suppliers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    contact_person VARCHAR(255),
    phone VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    city VARCHAR(100),
    tax_number VARCHAR(50),
    commercial_register VARCHAR(50),
    current_balance DECIMAL(15,2) DEFAULT 0.00,
    payment_terms VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_code (code),
    INDEX idx_name (name),
    INDEX idx_phone (phone),
    INDEX idx_active (is_active)
);

-- جدول الموظفين
CREATE TABLE employees (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_code VARCHAR(50) UNIQUE NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    position VARCHAR(100),
    department VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    hire_date DATE,
    salary DECIMAL(10,2) DEFAULT 0.00,
    branch_id INT,
    is_active BOOLEAN DEFAULT TRUE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE SET NULL,
    INDEX idx_code (employee_code),
    INDEX idx_name (full_name),
    INDEX idx_branch (branch_id),
    INDEX idx_active (is_active)
);

-- =====================================================
-- جداول الصناديق والبنوك (Cash & Banks)
-- =====================================================

-- جدول الصناديق
CREATE TABLE cash_registers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    branch_id INT NOT NULL,
    currency_code VARCHAR(3) DEFAULT 'SAR',
    opening_balance DECIMAL(15,2) DEFAULT 0.00,
    current_balance DECIMAL(15,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (branch_id) REFERENCES branches(id),
    INDEX idx_branch (branch_id),
    INDEX idx_active (is_active)
);

-- جدول البنوك
CREATE TABLE banks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    bank_name VARCHAR(255) NOT NULL,
    account_number VARCHAR(50) NOT NULL,
    account_name VARCHAR(255) NOT NULL,
    branch_name VARCHAR(255),
    iban VARCHAR(50),
    swift_code VARCHAR(20),
    currency_code VARCHAR(3) DEFAULT 'SAR',
    opening_balance DECIMAL(15,2) DEFAULT 0.00,
    current_balance DECIMAL(15,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_account_number (account_number),
    INDEX idx_bank_name (bank_name),
    INDEX idx_active (is_active)
);

-- جدول الملاك
CREATE TABLE owners (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    ownership_percentage DECIMAL(5,2) DEFAULT 0.00,
    capital_contribution DECIMAL(15,2) DEFAULT 0.00,
    current_balance DECIMAL(15,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_name (name),
    INDEX idx_active (is_active)
);

-- إدراج البيانات الأساسية
INSERT INTO company_settings (company_name_ar, company_name_en, phone, address)
VALUES ('مخبز أنوار الحي', 'Anwar Bakery', '', 'أدخل عنوان المخبز');

INSERT INTO users (username, password_hash, full_name, role)
VALUES ('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin');

INSERT INTO units (name, symbol, type) VALUES
('قطعة', 'قطعة', 'piece'),
('كيلوجرام', 'كجم', 'weight'),
('جرام', 'جم', 'weight'),
('لتر', 'لتر', 'volume');

-- إنشاء شجرة الحسابات الأساسية
INSERT INTO chart_of_accounts (account_code, account_name, account_type, level) VALUES
('1000', 'الأصول', 'assets', 1),
('1100', 'الأصول المتداولة', 'assets', 2),
('1110', 'النقدية', 'assets', 3),
('1120', 'المخزون', 'assets', 3),
('1130', 'العملاء', 'assets', 3),
('2000', 'الخصوم', 'liabilities', 1),
('2100', 'الخصوم المتداولة', 'liabilities', 2),
('2110', 'الموردين', 'liabilities', 3),
('3000', 'حقوق الملكية', 'equity', 1),
('3100', 'رأس المال', 'equity', 2),
('4000', 'الإيرادات', 'revenue', 1),
('4100', 'إيرادات المبيعات', 'revenue', 2),
('5000', 'المصروفات', 'expense', 1),
('5100', 'تكلفة البضاعة المباعة', 'expense', 2),
('5200', 'المصروفات التشغيلية', 'expense', 2);

-- =====================================================
-- جداول الفواتير والسندات (Invoices & Vouchers)
-- =====================================================

-- جدول الفواتير
CREATE TABLE invoices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    invoice_type ENUM('sales', 'purchase', 'sales_return', 'purchase_return') NOT NULL,
    invoice_date DATE NOT NULL,
    due_date DATE,
    customer_id INT NULL,
    supplier_id INT NULL,
    branch_id INT NOT NULL,
    subtotal DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    discount_amount DECIMAL(15,2) DEFAULT 0.00,
    discount_percentage DECIMAL(5,2) DEFAULT 0.00,
    tax_amount DECIMAL(15,2) DEFAULT 0.00,
    tax_percentage DECIMAL(5,2) DEFAULT 15.00,
    total_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    paid_amount DECIMAL(15,2) DEFAULT 0.00,
    remaining_amount DECIMAL(15,2) DEFAULT 0.00,
    payment_status ENUM('pending', 'partial', 'paid', 'overdue') DEFAULT 'pending',
    status ENUM('draft', 'confirmed', 'cancelled') DEFAULT 'draft',
    notes TEXT,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL,
    FOREIGN KEY (branch_id) REFERENCES branches(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_invoice_number (invoice_number),
    INDEX idx_type (invoice_type),
    INDEX idx_date (invoice_date),
    INDEX idx_customer (customer_id),
    INDEX idx_supplier (supplier_id),
    INDEX idx_status (status),
    INDEX idx_payment_status (payment_status)
);

-- جدول تفاصيل الفواتير
CREATE TABLE invoice_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity DECIMAL(10,2) NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(15,2) NOT NULL,
    notes VARCHAR(500),

    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id),
    INDEX idx_invoice (invoice_id),
    INDEX idx_product (product_id)
);

-- جدول السندات
CREATE TABLE vouchers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    voucher_number VARCHAR(50) UNIQUE NOT NULL,
    voucher_type ENUM('receipt', 'payment', 'journal') NOT NULL,
    voucher_date DATE NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    payment_method ENUM('cash', 'bank', 'check', 'transfer') DEFAULT 'cash',
    cash_register_id INT NULL,
    bank_id INT NULL,
    customer_id INT NULL,
    supplier_id INT NULL,
    employee_id INT NULL,
    reference_type ENUM('invoice', 'salary', 'expense', 'other') NULL,
    reference_id INT NULL,
    description TEXT NOT NULL,
    status ENUM('draft', 'confirmed', 'cancelled') DEFAULT 'draft',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (cash_register_id) REFERENCES cash_registers(id) ON DELETE SET NULL,
    FOREIGN KEY (bank_id) REFERENCES banks(id) ON DELETE SET NULL,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_voucher_number (voucher_number),
    INDEX idx_type (voucher_type),
    INDEX idx_date (voucher_date),
    INDEX idx_payment_method (payment_method),
    INDEX idx_reference (reference_type, reference_id),
    INDEX idx_status (status)
);

-- =====================================================
-- جداول النظام المتقدمة (Advanced System Tables)
-- =====================================================

-- جدول النسخ الاحتياطية
CREATE TABLE system_backups (
    id INT PRIMARY KEY AUTO_INCREMENT,
    backup_name VARCHAR(255) NOT NULL,
    backup_type ENUM('manual', 'automatic', 'scheduled') DEFAULT 'manual',
    file_path VARCHAR(500),
    file_size BIGINT DEFAULT 0,
    backup_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT NULL,
    status ENUM('in_progress', 'completed', 'failed') DEFAULT 'completed',
    notes TEXT,

    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_date (backup_date),
    INDEX idx_type (backup_type),
    INDEX idx_status (status)
);

-- جدول سجل النظام
CREATE TABLE system_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100),
    record_id INT NULL,
    old_values JSON NULL,
    new_values JSON NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user (user_id),
    INDEX idx_action (action),
    INDEX idx_table (table_name),
    INDEX idx_date (created_at)
);

-- جدول إعدادات النظام
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    category VARCHAR(50) DEFAULT 'general',
    description TEXT,
    is_editable BOOLEAN DEFAULT TRUE,
    updated_by INT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_key (setting_key),
    INDEX idx_category (category)
);

-- جدول السنوات المالية
CREATE TABLE fiscal_years (
    id INT PRIMARY KEY AUTO_INCREMENT,
    year_name VARCHAR(50) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_current BOOLEAN DEFAULT FALSE,
    is_closed BOOLEAN DEFAULT FALSE,
    closing_date DATE NULL,
    closed_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (closed_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_year (year_name),
    INDEX idx_current (is_current),
    INDEX idx_dates (start_date, end_date)
);

-- جدول الأرصدة الافتتاحية
CREATE TABLE opening_balances (
    id INT PRIMARY KEY AUTO_INCREMENT,
    fiscal_year_id INT NOT NULL,
    account_id INT NOT NULL,
    opening_balance DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (fiscal_year_id) REFERENCES fiscal_years(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id) ON DELETE CASCADE,
    UNIQUE KEY unique_year_account (fiscal_year_id, account_id),
    INDEX idx_fiscal_year (fiscal_year_id),
    INDEX idx_account (account_id)
);

-- =====================================================
-- إدراج البيانات الأساسية للنظام
-- =====================================================

-- إعدادات النظام الأساسية
INSERT INTO system_settings (setting_key, setting_value, setting_type, category, description) VALUES
('app_name', 'نظام إدارة مخبز أنوار الحي', 'string', 'general', 'اسم التطبيق'),
('app_version', '1.0.0', 'string', 'general', 'إصدار التطبيق'),
('default_currency', 'SAR', 'string', 'financial', 'العملة الافتراضية'),
('tax_rate', '15.00', 'number', 'financial', 'معدل الضريبة الافتراضي'),
('backup_retention_days', '30', 'number', 'system', 'عدد أيام الاحتفاظ بالنسخ الاحتياطية'),
('auto_backup_enabled', 'true', 'boolean', 'system', 'تفعيل النسخ الاحتياطية التلقائية'),
('auto_backup_time', '02:00', 'string', 'system', 'وقت النسخ الاحتياطي التلقائي'),
('invoice_prefix_sales', 'INV-S-', 'string', 'invoices', 'بادئة فواتير المبيعات'),
('invoice_prefix_purchase', 'INV-P-', 'string', 'invoices', 'بادئة فواتير المشتريات'),
('voucher_prefix_receipt', 'REC-', 'string', 'vouchers', 'بادئة سندات القبض'),
('voucher_prefix_payment', 'PAY-', 'string', 'vouchers', 'بادئة سندات الصرف');

-- السنة المالية الحالية
INSERT INTO fiscal_years (year_name, start_date, end_date, is_current)
VALUES ('2024', '2024-01-01', '2024-12-31', TRUE);

-- فرع رئيسي افتراضي
INSERT INTO branches (name, type, address, is_active)
VALUES ('الفرع الرئيسي', 'both', 'الرياض، المملكة العربية السعودية', TRUE);

-- صندوق رئيسي افتراضي
INSERT INTO cash_registers (name, branch_id, opening_balance, current_balance)
VALUES ('الصندوق الرئيسي', 1, 0.00, 0.00);
