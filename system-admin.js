// System Administration Management
let systemData = {
    fiscalYear: 2024,
    backups: [],
    systemHealth: 'healthy',
    lastBackup: null,
    dataSize: 0
};

// Check authentication and admin privileges
function checkAdminAuth() {
    const session = localStorage.getItem('anwar_bakery_session') ||
                   sessionStorage.getItem('anwar_bakery_session');

    if (!session) {
        window.location.href = 'login.html';
        return null;
    }

    const sessionData = JSON.parse(session);
    if (sessionData.role !== 'admin') {
        alert('⚠️ هذه الصفحة مخصصة للمديرين فقط!');
        window.location.href = 'dashboard.html';
        return null;
    }

    return sessionData;
}

// Load user info
function loadUserInfo() {
    const session = checkAdminAuth();
    if (session) {
        document.getElementById('userFullName').textContent = session.fullName;
        document.getElementById('userRole').textContent = session.role;
    }
}

// Load company info
function loadCompanyInfo() {
    const savedData = localStorage.getItem('anwar_bakery_company');
    if (savedData) {
        const companyData = JSON.parse(savedData);
        const sidebarElement = document.getElementById('sidebarCompanyName');
        if (sidebarElement && companyData.companyNameAr) {
            sidebarElement.textContent = companyData.companyNameAr;
        }
    }
}

// Toggle sidebar
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobileOverlay');

    if (sidebar && overlay) {
        if (sidebar.classList.contains('translate-x-full')) {
            sidebar.classList.remove('translate-x-full');
            overlay.classList.remove('hidden');
        } else {
            sidebar.classList.add('translate-x-full');
            overlay.classList.add('hidden');
        }
    }
}

// Update current date time
function updateDateTime() {
    const now = new Date();
    const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    const dateTimeElement = document.getElementById('currentDateTime');
    if (dateTimeElement) {
        dateTimeElement.textContent = now.toLocaleDateString('ar-SA', options);
    }
}

// Calculate data size
function calculateDataSize() {
    let totalSize = 0;
    for (let key in localStorage) {
        if (key.startsWith('anwar_bakery_')) {
            totalSize += localStorage[key].length;
        }
    }
    return (totalSize / 1024).toFixed(1) + ' KB';
}

// Update system status
function updateSystemStatus() {
    document.getElementById('dataSize').textContent = calculateDataSize();

    const lastBackup = localStorage.getItem('anwar_bakery_last_backup');
    if (lastBackup) {
        const backupDate = new Date(lastBackup);
        const now = new Date();
        const diffDays = Math.floor((now - backupDate) / (1000 * 60 * 60 * 24));

        if (diffDays === 0) {
            document.getElementById('lastBackup').textContent = 'اليوم';
        } else if (diffDays === 1) {
            document.getElementById('lastBackup').textContent = 'أمس';
        } else {
            document.getElementById('lastBackup').textContent = `منذ ${diffDays} أيام`;
        }
    } else {
        document.getElementById('lastBackup').textContent = 'لم يتم';
    }
}

// Financial Year Management
function openYearEndModal() {
    if (confirm('⚠️ هل أنت متأكد من ترحيل نهاية السنة؟\n\nسيتم:\n- إقفال جميع الحسابات\n- ترحيل الأرصدة للسنة الجديدة\n- إنشاء نسخة احتياطية تلقائية\n\nهذه العملية لا يمكن التراجع عنها!')) {
        performYearEndProcess();
    }
}

function performYearEndProcess() {
    showMessage('🔄 جاري ترحيل نهاية السنة...', 'info');

    // Simulate year-end process
    setTimeout(() => {
        // Create backup before year-end
        createAutomaticBackup('year_end_backup');

        // Close all revenue and expense accounts
        closeRevenueExpenseAccounts();

        // Transfer balances to new year
        transferBalancesToNewYear();

        // Update fiscal year
        systemData.fiscalYear += 1;
        localStorage.setItem('anwar_bakery_fiscal_year', systemData.fiscalYear.toString());
        document.getElementById('fiscalYear').textContent = systemData.fiscalYear;

        showMessage('✅ تم ترحيل نهاية السنة بنجاح! تم إنشاء نسخة احتياطية تلقائية.', 'success');
    }, 3000);
}

function closeRevenueExpenseAccounts() {
    // Get all accounts
    const accounts = JSON.parse(localStorage.getItem('anwar_bakery_accounts') || '[]');
    const journalEntries = JSON.parse(localStorage.getItem('anwar_bakery_journal_entries') || '[]');

    // Create closing entries for revenue and expense accounts
    const closingEntry = {
        id: 'closing_' + Date.now(),
        date: new Date().toISOString().split('T')[0],
        description: 'قيد إقفال نهاية السنة المالية ' + systemData.fiscalYear,
        type: 'closing',
        entries: []
    };

    // Add closing entries logic here
    journalEntries.push(closingEntry);
    localStorage.setItem('anwar_bakery_journal_entries', JSON.stringify(journalEntries));
}

function transferBalancesToNewYear() {
    // Transfer asset, liability, and equity balances to opening balances
    const openingBalances = {};
    const accounts = JSON.parse(localStorage.getItem('anwar_bakery_accounts') || '[]');

    accounts.forEach(account => {
        if (['assets', 'liabilities', 'equity'].includes(account.type)) {
            openingBalances[account.id] = account.balance || 0;
        }
    });

    localStorage.setItem('anwar_bakery_opening_balances_' + (systemData.fiscalYear + 1), JSON.stringify(openingBalances));
}

function openNewYearModal() {
    const newYear = prompt('أدخل السنة المالية الجديدة:', (systemData.fiscalYear + 1).toString());
    if (newYear && !isNaN(newYear)) {
        startNewFiscalYear(parseInt(newYear));
    }
}

function startNewFiscalYear(year) {
    if (confirm(`هل تريد بدء السنة المالية ${year}؟\n\nسيتم:\n- تعيين السنة المالية الجديدة\n- تطبيق الأرصدة الافتتاحية\n- إنشاء نسخة احتياطية`)) {
        systemData.fiscalYear = year;
        localStorage.setItem('anwar_bakery_fiscal_year', year.toString());
        document.getElementById('fiscalYear').textContent = year;

        // Apply opening balances
        applyOpeningBalances(year);

        showMessage(`✅ تم بدء السنة المالية ${year} بنجاح!`, 'success');
    }
}

function applyOpeningBalances(year) {
    const openingBalances = JSON.parse(localStorage.getItem('anwar_bakery_opening_balances_' + year) || '{}');
    const accounts = JSON.parse(localStorage.getItem('anwar_bakery_accounts') || '[]');

    accounts.forEach(account => {
        if (openingBalances[account.id]) {
            account.openingBalance = openingBalances[account.id];
            account.balance = openingBalances[account.id];
        }
    });

    localStorage.setItem('anwar_bakery_accounts', JSON.stringify(accounts));
}

function viewOpeningBalances() {
    const year = systemData.fiscalYear;
    const openingBalances = JSON.parse(localStorage.getItem('anwar_bakery_opening_balances_' + year) || '{}');
    const accounts = JSON.parse(localStorage.getItem('anwar_bakery_accounts') || '[]');

    let report = `الأرصدة الافتتاحية للسنة المالية ${year}\n\n`;

    accounts.forEach(account => {
        if (openingBalances[account.id]) {
            report += `${account.name}: ${openingBalances[account.id].toLocaleString()}\n`;
        }
    });

    if (Object.keys(openingBalances).length === 0) {
        report += 'لا توجد أرصدة افتتاحية مسجلة';
    }

    alert(report);
}

// Backup and Restore Functions
function createManualBackup() {
    if (confirm('هل تريد إنشاء نسخة احتياطية يدوية؟')) {
        createBackup('manual_backup');
    }
}

function createBackup(type = 'manual') {
    showMessage('🔄 جاري إنشاء النسخة الاحتياطية...', 'info');

    setTimeout(() => {
        const backupData = {
            timestamp: new Date().toISOString(),
            type: type,
            data: {}
        };

        // Collect all system data
        for (let key in localStorage) {
            if (key.startsWith('anwar_bakery_')) {
                backupData.data[key] = localStorage[key];
            }
        }

        // Save backup
        const backupKey = `anwar_bakery_backup_${Date.now()}`;
        localStorage.setItem(backupKey, JSON.stringify(backupData));
        localStorage.setItem('anwar_bakery_last_backup', new Date().toISOString());

        // Update backup list
        const backups = JSON.parse(localStorage.getItem('anwar_bakery_backup_list') || '[]');
        backups.push({
            key: backupKey,
            timestamp: backupData.timestamp,
            type: type,
            size: JSON.stringify(backupData).length
        });
        localStorage.setItem('anwar_bakery_backup_list', JSON.stringify(backups));

        updateSystemStatus();
        showMessage('✅ تم إنشاء النسخة الاحتياطية بنجاح!', 'success');
    }, 2000);
}

function createAutomaticBackup(type) {
    createBackup(type);
}

function openRestoreModal() {
    const backups = JSON.parse(localStorage.getItem('anwar_bakery_backup_list') || '[]');

    if (backups.length === 0) {
        alert('لا توجد نسخ احتياطية متاحة');
        return;
    }

    let backupList = 'اختر النسخة الاحتياطية للاستعادة:\n\n';
    backups.forEach((backup, index) => {
        const date = new Date(backup.timestamp).toLocaleString('ar-SA');
        backupList += `${index + 1}. ${backup.type} - ${date}\n`;
    });

    const choice = prompt(backupList + '\nأدخل رقم النسخة:');
    if (choice && !isNaN(choice)) {
        const index = parseInt(choice) - 1;
        if (index >= 0 && index < backups.length) {
            restoreBackup(backups[index].key);
        }
    }
}

function restoreBackup(backupKey) {
    if (confirm('⚠️ هل أنت متأكد من استعادة هذه النسخة؟\n\nسيتم استبدال جميع البيانات الحالية!')) {
        showMessage('🔄 جاري استعادة النسخة الاحتياطية...', 'info');

        setTimeout(() => {
            const backupData = JSON.parse(localStorage.getItem(backupKey));

            if (backupData && backupData.data) {
                // Clear current data
                for (let key in localStorage) {
                    if (key.startsWith('anwar_bakery_') && !key.startsWith('anwar_bakery_backup_')) {
                        localStorage.removeItem(key);
                    }
                }

                // Restore backup data
                for (let key in backupData.data) {
                    localStorage.setItem(key, backupData.data[key]);
                }

                showMessage('✅ تم استعادة النسخة الاحتياطية بنجاح! سيتم إعادة تحميل الصفحة...', 'success');

                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                showMessage('❌ فشل في استعادة النسخة الاحتياطية!', 'error');
            }
        }, 2000);
    }
}

function viewBackupHistory() {
    const backups = JSON.parse(localStorage.getItem('anwar_bakery_backup_list') || '[]');

    if (backups.length === 0) {
        alert('لا توجد نسخ احتياطية');
        return;
    }

    let history = 'سجل النسخ الاحتياطية:\n\n';
    backups.forEach((backup, index) => {
        const date = new Date(backup.timestamp).toLocaleString('ar-SA');
        const size = (backup.size / 1024).toFixed(1) + ' KB';
        history += `${index + 1}. ${backup.type}\n   التاريخ: ${date}\n   الحجم: ${size}\n\n`;
    });

    alert(history);
}

// Data Management Functions
function exportAllData() {
    showMessage('🔄 جاري تصدير البيانات...', 'info');

    setTimeout(() => {
        const allData = {};
        for (let key in localStorage) {
            if (key.startsWith('anwar_bakery_')) {
                allData[key] = localStorage[key];
            }
        }

        const dataStr = JSON.stringify(allData, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `anwar_bakery_export_${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        showMessage('✅ تم تصدير البيانات بنجاح!', 'success');
    }, 1000);
}

function importData() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);

                    if (confirm('هل تريد استيراد هذه البيانات؟\n\nسيتم استبدال البيانات الحالية!')) {
                        // Clear current data
                        for (let key in localStorage) {
                            if (key.startsWith('anwar_bakery_')) {
                                localStorage.removeItem(key);
                            }
                        }

                        // Import new data
                        for (let key in data) {
                            localStorage.setItem(key, data[key]);
                        }

                        showMessage('✅ تم استيراد البيانات بنجاح!', 'success');
                        setTimeout(() => window.location.reload(), 2000);
                    }
                } catch (error) {
                    showMessage('❌ خطأ في قراءة الملف!', 'error');
                }
            };
            reader.readAsText(file);
        }
    };
    input.click();
}

function cleanupData() {
    if (confirm('هل تريد تنظيف البيانات؟\n\nسيتم حذف:\n- البيانات المكررة\n- السجلات الفارغة\n- النسخ الاحتياطية القديمة')) {
        showMessage('🔄 جاري تنظيف البيانات...', 'info');

        setTimeout(() => {
            // Cleanup logic here
            let cleanedItems = 0;

            // Remove old backups (keep only last 10)
            const backups = JSON.parse(localStorage.getItem('anwar_bakery_backup_list') || '[]');
            if (backups.length > 10) {
                const toRemove = backups.slice(0, backups.length - 10);
                toRemove.forEach(backup => {
                    localStorage.removeItem(backup.key);
                    cleanedItems++;
                });

                const newBackups = backups.slice(-10);
                localStorage.setItem('anwar_bakery_backup_list', JSON.stringify(newBackups));
            }

            showMessage(`✅ تم تنظيف ${cleanedItems} عنصر من البيانات!`, 'success');
            updateSystemStatus();
        }, 2000);
    }
}

// System Diagnostics
async function runSystemDiagnostics() {
    // تحذير للمستخدم أن الفحص يتم على بيانات المتصفح فقط
    showMessage('⚠️ فحص النظام يعمل فقط على البيانات المخزنة في المتصفح (localStorage) ولا يعكس قاعدة بيانات MySQL. تأكد من مزامنة البيانات مع السيرفر!', 'warning');

    try {
        // Run comprehensive diagnostics
        const report = await window.systemDiagnostics.runFullDiagnostics();

        // Display detailed report
        showDiagnosticReport(report);

        showMessage(`✅ تم فحص النظام! وُجدت ${report.totalIssues} مشكلة، تم إصلاح ${report.autoFixed} منها تلقائياً`, 'success');
    } catch (error) {
        console.error('Diagnostic error:', error);
        showMessage('❌ خطأ في فحص النظام!', 'error');
    }
}

function showDiagnosticReport(report) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold">تقرير فحص النظام</h3>
                    <button onclick="this.closest('.fixed').remove()" class="text-gray-500 hover:text-gray-700">
                        <span class="text-2xl">×</span>
                    </button>
                </div>

                <!-- Summary -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-red-50 p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-red-600">${report.errors}</div>
                        <div class="text-sm text-red-700">أخطاء</div>
                    </div>
                    <div class="bg-yellow-50 p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-yellow-600">${report.warnings}</div>
                        <div class="text-sm text-yellow-700">تحذيرات</div>
                    </div>
                    <div class="bg-blue-50 p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-blue-600">${report.info}</div>
                        <div class="text-sm text-blue-700">معلومات</div>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-green-600">${report.autoFixed}</div>
                        <div class="text-sm text-green-700">تم إصلاحها</div>
                    </div>
                </div>

                <!-- Issues List -->
                <div class="space-y-3 max-h-96 overflow-y-auto">
                    ${report.issues.map(issue => `
                        <div class="border rounded-lg p-3 ${getIssueColorClass(issue.type)}">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="text-lg ml-2">${getIssueIcon(issue.type)}</span>
                                    <div>
                                        <div class="font-medium">${issue.message}</div>
                                        <div class="text-sm text-gray-600">${issue.category}</div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    ${issue.fixed ? '<span class="text-green-600 text-sm">✅ تم الإصلاح</span>' : ''}
                                    ${issue.autoFixable && !issue.fixed ? '<span class="text-blue-600 text-sm">🔧 قابل للإصلاح</span>' : ''}
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>

                <!-- Actions -->
                <div class="mt-6 flex space-x-3">
                    <button onclick="exportDiagnosticReport(${JSON.stringify(report).replace(/"/g, '&quot;')})"
                            class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                        📊 تصدير التقرير
                    </button>
                    <button onclick="runAutoFix()"
                            class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                        🔧 إصلاح تلقائي
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

function getIssueColorClass(type) {
    switch (type) {
        case 'error': return 'border-red-200 bg-red-50';
        case 'warning': return 'border-yellow-200 bg-yellow-50';
        case 'info': return 'border-blue-200 bg-blue-50';
        default: return 'border-gray-200 bg-gray-50';
    }
}

function getIssueIcon(type) {
    switch (type) {
        case 'error': return '❌';
        case 'warning': return '⚠️';
        case 'info': return 'ℹ️';
        default: return '📋';
    }
}

async function runAutoFix() {
    if (confirm('هل تريد تشغيل الإصلاح التلقائي؟\n\nسيتم إنشاء نسخة احتياطية قبل الإصلاح.')) {
        showMessage('🔧 جاري الإصلاح التلقائي...', 'info');

        try {
            await window.systemDiagnostics.autoFixIssues();
            showMessage('✅ تم الإصلاح التلقائي بنجاح!', 'success');

            // Re-run diagnostics to show updated status
            setTimeout(() => runSystemDiagnostics(), 1000);
        } catch (error) {
            console.error('Auto-fix error:', error);
            showMessage('❌ خطأ في الإصلاح التلقائي!', 'error');
        }
    }
}

function exportDiagnosticReport(report) {
    const reportData = report.issues.map(issue => ({
        'النوع': issue.type,
        'الفئة': issue.category,
        'الرسالة': issue.message,
        'قابل للإصلاح': issue.autoFixable ? 'نعم' : 'لا',
        'تم الإصلاح': issue.fixed ? 'نعم' : 'لا'
    }));

    window.exportToExcel(reportData, 'تقرير_فحص_النظام', 'تقرير الفحص');
}

function checkDataIntegrityQuick() {
    // Quick data integrity check
    try {
        const accounts = JSON.parse(localStorage.getItem('anwar_bakery_accounts') || '[]');
        const vouchers = JSON.parse(localStorage.getItem('anwar_bakery_vouchers') || '[]');
        return accounts.length >= 0 && vouchers.length >= 0;
    } catch (error) {
        return false;
    }
}

function checkAccountBalanceQuick() {
    // Quick balance check
    try {
        const journalEntries = JSON.parse(localStorage.getItem('anwar_bakery_journal_entries') || '[]');
        let totalDebit = 0, totalCredit = 0;

        journalEntries.forEach(entry => {
            if (entry.entries) {
                entry.entries.forEach(line => {
                    totalDebit += line.debit || 0;
                    totalCredit += line.credit || 0;
                });
            }
        });

        return Math.abs(totalDebit - totalCredit) < 0.01;
    } catch (error) {
        return false;
    }
}

// Danger Zone Functions
function resetSystem() {
    if (confirm('⚠️ هل أنت متأكد من إعادة تعيين النظام؟\n\nسيتم:\n- حذف جميع البيانات\n- الاحتفاظ بالإعدادات الأساسية\n- إنشاء نسخة احتياطية قبل الحذف')) {
        createAutomaticBackup('before_reset');

        setTimeout(() => {
            // Keep only essential settings
            const essentialKeys = ['anwar_bakery_company', 'anwar_bakery_session'];
            const essentialData = {};

            essentialKeys.forEach(key => {
                if (localStorage.getItem(key)) {
                    essentialData[key] = localStorage.getItem(key);
                }
            });

            // Clear all data
            for (let key in localStorage) {
                if (key.startsWith('anwar_bakery_')) {
                    localStorage.removeItem(key);
                }
            }

            // Restore essential data
            for (let key in essentialData) {
                localStorage.setItem(key, essentialData[key]);
            }

            showMessage('✅ تم إعادة تعيين النظام بنجاح!', 'success');
            setTimeout(() => window.location.reload(), 2000);
        }, 2000);
    }
}

function clearAllData() {
    const confirmation = prompt('⚠️ تحذير: هذا سيحذف جميع البيانات!\n\nاكتب "حذف جميع البيانات" للتأكيد:');

    if (confirmation === 'حذف جميع البيانات') {
        for (let key in localStorage) {
            if (key.startsWith('anwar_bakery_')) {
                localStorage.removeItem(key);
            }
        }

        alert('تم حذف جميع البيانات!');
        window.location.href = 'login.html';
    }
}

function factoryReset() {
    const confirmation = prompt('⚠️ تحذير: إعادة ضبط المصنع!\n\nسيتم حذف كل شيء!\n\nاكتب "إعادة ضبط المصنع" للتأكيد:');

    if (confirmation === 'إعادة ضبط المصنع') {
        localStorage.clear();
        sessionStorage.clear();
        alert('تم إعادة ضبط المصنع!');
        window.location.href = 'index.html';
    }
}

// تصفير قاعدة البيانات MySQL مع الإبقاء على شجرة الحسابات
function resetDatabaseMySQL() {
    if (!confirm('⚠️ هل أنت متأكد من تصفير جميع بيانات قاعدة البيانات (ماعدا شجرة الحسابات)؟\n\nسيتم حذف جميع البيانات من قاعدة البيانات مباشرة ولا يمكن التراجع!')) {
        return;
    }
    fetch('api/reset-database.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
    })
    .then(res => res.json())
    .then(data => {
        if (data.success) {
            alert('✅ تم تصفير قاعدة البيانات بنجاح!');
            window.location.reload();
        } else {
            alert('❌ حدث خطأ: ' + (data.error || 'لم يتم التصفير'));
        }
    })
    .catch(err => {
        alert('❌ فشل الاتصال بسيرفر قاعدة البيانات: ' + err);
    });
}

// Show message function
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    if (!container) return;

    const alertClass = {
        'success': 'bg-green-50 border-green-200 text-green-700',
        'error': 'bg-red-50 border-red-200 text-red-700',
        'info': 'bg-blue-50 border-blue-200 text-blue-700',
        'warning': 'bg-yellow-50 border-yellow-200 text-yellow-700'
    };

    container.innerHTML = `
        <div class="border rounded-lg p-4 ${alertClass[type]}">
            <div class="flex items-center">
                <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️'}</span>
                ${message}
            </div>
        </div>
    `;

    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_session');
        sessionStorage.removeItem('anwar_bakery_session');
        window.location.href = 'login.html';
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadUserInfo();
    loadCompanyInfo();
    updateDateTime();
    updateSystemStatus();
    setInterval(updateDateTime, 60000);

    // Load fiscal year
    const savedFiscalYear = localStorage.getItem('anwar_bakery_fiscal_year');
    if (savedFiscalYear) {
        systemData.fiscalYear = parseInt(savedFiscalYear);
        document.getElementById('fiscalYear').textContent = systemData.fiscalYear;
    }

    // Close sidebar when clicking overlay
    const overlay = document.getElementById('mobileOverlay');
    if (overlay) {
        overlay.addEventListener('click', toggleSidebar);
    }

    // Setup automatic backup (simulate)
    setInterval(() => {
        const now = new Date();
        if (now.getHours() === 2 && now.getMinutes() === 0) {
            createAutomaticBackup('automatic');
        }
    }, 60000); // Check every minute

    // Initialize auto sync toggle
    if (typeof window.syncClient !== 'undefined') {
        const autoSyncEnabled = localStorage.getItem('anwar_bakery_auto_sync') === 'true';
        const toggle = document.getElementById('autoSyncToggle');
        if (toggle) {
            toggle.checked = autoSyncEnabled;
        }
    }

    // تحديث تلقائي لحجم البيانات وحالة النظام كل دقيقة
    setInterval(updateSystemStatus, 60000);
});

// Advanced Features Functions
// دوال الميزات المتقدمة

/**
 * Test advanced invoice printing
 * اختبار طباعة الفاتورة المتقدمة
 */
function testPrintInvoice() {
    // Create sample invoice data
    const sampleInvoice = {
        number: 'INV-2024-001',
        date: new Date().toISOString().split('T')[0],
        time: new Date().toISOString(),
        type: 'sales',
        customer_id: 1,
        items: [
            {
                name: 'خبز أبيض',
                quantity: 5,
                unit: 'رغيف',
                price: 2.50,
                total: 12.50
            },
            {
                name: 'كيك شوكولاتة',
                quantity: 1,
                unit: 'قطعة',
                price: 25.00,
                total: 25.00
            }
        ],
        subtotal: 37.50,
        discount: 0,
        tax: 5.63,
        taxRate: 15,
        total: 43.13,
        notes: 'شكراً لتعاملكم معنا'
    };

    if (typeof window.advancedPrint !== 'undefined') {
        window.advancedPrint.printInvoice(sampleInvoice, {
            showLogo: true,
            showHeader: true,
            showFooter: true,
            copies: 1,
            paperSize: 'A4'
        });
        showMessage('تم إرسال الفاتورة للطباعة', 'success');
    } else {
        showMessage('نظام الطباعة المتقدم غير متوفر', 'error');
    }
}

/**
 * Test thermal receipt printing
 * اختبار طباعة الإيصال الحراري
 */
function testPrintReceipt() {
    const sampleInvoice = {
        number: 'REC-2024-001',
        date: new Date().toISOString().split('T')[0],
        items: [
            { name: 'خبز أبيض', quantity: 5, price: 2.50, total: 12.50 },
            { name: 'كيك شوكولاتة', quantity: 1, price: 25.00, total: 25.00 }
        ],
        total: 37.50
    };

    if (typeof window.advancedPrint !== 'undefined') {
        window.advancedPrint.printReceipt(sampleInvoice, {
            width: '80mm',
            fontSize: '12px'
        });
        showMessage('تم إرسال الإيصال للطباعة', 'success');
    } else {
        showMessage('نظام الطباعة المتقدم غير متوفر', 'error');
    }
}

/**
 * Export advanced sales report
 * تصدير تقرير مبيعات متقدم
 */
function exportAdvancedSalesReport() {
    try {
        // Get sales data
        const invoices = JSON.parse(localStorage.getItem('anwar_bakery_invoices') || '[]');
        const salesInvoices = invoices.filter(inv => inv.type === 'sales');

        // Prepare report data
        const reportData = {
            main: salesInvoices.map(inv => ({
                'رقم الفاتورة': inv.number,
                'التاريخ': inv.date,
                'العميل': inv.customer_name || 'عميل نقدي',
                'المبلغ الفرعي': inv.subtotal || 0,
                'الخصم': inv.discount || 0,
                'الضريبة': inv.tax || 0,
                'الإجمالي': inv.total || 0,
                'طريقة الدفع': inv.payment_method || 'نقدي'
            })),
            summary: {
                totalSales: salesInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0),
                totalInvoices: salesInvoices.length,
                averageInvoice: salesInvoices.length > 0 ?
                    salesInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0) / salesInvoices.length : 0
            }
        };

        if (typeof window.advancedExcel !== 'undefined') {
            const result = window.advancedExcel.exportFinancialReport(
                reportData,
                'sales_report',
                { month: new Date().getMonth() + 1, year: new Date().getFullYear() }
            );

            if (result.success) {
                showMessage('تم تصدير تقرير المبيعات المتقدم بنجاح', 'success');
            } else {
                showMessage('فشل في تصدير التقرير: ' + result.message, 'error');
            }
        } else {
            showMessage('نظام التصدير المتقدم غير متوفر', 'error');
        }
    } catch (error) {
        console.error('Export error:', error);
        showMessage('خطأ في تصدير التقرير: ' + error.message, 'error');
    }
}

/**
 * Export business dashboard
 * تصدير لوحة التحكم الشاملة
 */
function exportBusinessDashboard() {
    try {
        // Collect all business data
        const invoices = JSON.parse(localStorage.getItem('anwar_bakery_invoices') || '[]');
        const products = JSON.parse(localStorage.getItem('anwar_bakery_products') || '[]');
        const customers = JSON.parse(localStorage.getItem('anwar_bakery_customers') || '[]');
        const suppliers = JSON.parse(localStorage.getItem('anwar_bakery_suppliers') || '[]');

        // Calculate key metrics
        const salesInvoices = invoices.filter(inv => inv.type === 'sales');
        const purchaseInvoices = invoices.filter(inv => inv.type === 'purchase');

        const dashboardData = {
            summary: {
                totalSales: salesInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0),
                totalPurchases: purchaseInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0),
                totalProducts: products.length,
                totalCustomers: customers.length,
                totalSuppliers: suppliers.length,
                lowStockProducts: products.filter(p => (p.quantity || 0) < (p.min_stock_level || 5)).length
            },
            financial: {
                revenue: salesInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0),
                expenses: purchaseInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0),
                profit: salesInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0) -
                       purchaseInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0)
            },
            operations: {
                dailyTransactions: invoices.length,
                topProducts: products.slice(0, 10),
                recentCustomers: customers.slice(-10)
            },
            kpis: [
                { metric: 'إجمالي المبيعات', value: salesInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0) },
                { metric: 'عدد الفواتير', value: invoices.length },
                { metric: 'متوسط الفاتورة', value: invoices.length > 0 ? invoices.reduce((sum, inv) => sum + (inv.total || 0), 0) / invoices.length : 0 },
                { metric: 'عدد المنتجات', value: products.length },
                { metric: 'عدد العملاء', value: customers.length }
            ]
        };

        if (typeof window.advancedExcel !== 'undefined') {
            const result = window.advancedExcel.exportBusinessDashboard(dashboardData);

            if (result.success) {
                showMessage('تم تصدير لوحة التحكم الشاملة بنجاح', 'success');
            } else {
                showMessage('فشل في تصدير لوحة التحكم: ' + result.message, 'error');
            }
        } else {
            showMessage('نظام التصدير المتقدم غير متوفر', 'error');
        }
    } catch (error) {
        console.error('Dashboard export error:', error);
        showMessage('خطأ في تصدير لوحة التحكم: ' + error.message, 'error');
    }
}

/**
 * Toggle auto sync
 * تبديل المزامنة التلقائية
 */
function toggleAutoSync(enabled) {
    if (typeof window.syncClient !== 'undefined') {
        window.syncClient.setAutoSync(enabled);
        showMessage(enabled ? 'تم تفعيل المزامنة التلقائية' : 'تم إيقاف المزامنة التلقائية', 'info');
    } else {
        showMessage('نظام المزامنة غير متوفر', 'error');
    }
}
