<?php
// api/get-branches.php
header('Content-Type: application/json');
require_once '../config.php';

$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed']);
    exit;
}

$sql = "SELECT id, branchCode, branchName, city, district, address, manager, phone, email, openingDate, isActive, notes, createdAt FROM branches ORDER BY branchName ASC";
$result = $conn->query($sql);

$branches = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $row['isActive'] = (bool)$row['isActive'];
        $branches[] = $row;
    }
}

$conn->close();
echo json_encode(['branches' => $branches]);
