<?php
// Reset Database Except Chart of Accounts
require_once __DIR__ . '/config/database.php';

header('Content-Type: application/json');

try {
    $db = new Database();
    $conn = $db->getConnection();

    // Disable foreign key checks
    $conn->exec('SET FOREIGN_KEY_CHECKS = 0');

    // List of tables to truncate (except chart_of_accounts)
    $tables = [
        'users',
        'branches',
        'units',
        'product_categories',
        'products',
        'inventory_movements',
        'customers',
        'suppliers',
        'journal_entries',
        'journal_entry_details',
        'company_settings'
        // Add more tables as needed
    ];
    foreach ($tables as $table) {
        $conn->exec("TRUNCATE TABLE `$table`");
    }

    // Reset balances in chart_of_accounts
    $conn->exec('UPDATE chart_of_accounts SET opening_balance = 0, current_balance = 0');

    // Enable foreign key checks
    $conn->exec('SET FOREIGN_KEY_CHECKS = 1');

    echo json_encode(['success' => true, 'message' => 'تم تصفير جميع البيانات ماعدا شجرة الحسابات.']);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
